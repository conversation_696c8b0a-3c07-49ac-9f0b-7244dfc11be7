# Cashfree Payment & Payout Integration Summary

This document provides a comprehensive overview of the Cashfree payment and payout integration across all apps in the monorepo.

## 🎯 **Integration Overview**

The Cashfree payment and payout system has been successfully integrated across all four applications with the following capabilities:

### **Payment Processing (Collections)**

1. **kabadiwala-mobile**: Wallet top-ups via Cashfree
2. **scraphub**: Wallet deposits via Cashfree
3. **scraplo-web**: Customer order payments via Cashfree

### **Payout Processing (Transfers)**

1. **scraplo-web**: Seller beneficiary creation and payout receiving
2. **kabadiwala-mobile**: Wallet withdrawals and seller payout triggers
3. **scraphub**: Wallet withdrawals with minimum balance enforcement

### **Centralized Management**

4. **admin**: Unified webhook handling for both payments and payouts

## 📊 **Database Schema Updates**

### New Tables Added

#### `payout` - Unified Payout Tracking

```sql
CREATE TABLE payout (
  id VARCHAR(128) PRIMARY KEY,
  transferId TEXT NOT NULL UNIQUE,
  cfTransferId TEXT,
  userType ENUM('SELLER', 'KABADIWALA', 'SCRAPHUB') NOT NULL,
  userId TEXT NOT NULL,
  beneficiaryId TEXT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'INR',
  payoutType ENUM('WALLET_WITHDRAWAL', 'ORDER_PAYOUT', 'REFUND', 'MANUAL_PAYOUT') NOT NULL,
  transferMode TEXT NOT NULL,
  status ENUM('RECEIVED', 'APPROVAL_PENDING', 'PENDING', 'SUCCESS', 'FAILED', 'REJECTED', 'REVERSED') DEFAULT 'RECEIVED',
  statusDescription TEXT,
  transferServiceCharge DECIMAL(10,2),
  transferServiceTax DECIMAL(10,2),
  transferUtr TEXT,
  remarks TEXT,
  purpose TEXT,
  gatewayMetadata JSONB,
  createdAt TIMESTAMP DEFAULT NOW(),
  updatedAt TIMESTAMP DEFAULT NOW(),
  processedAt TIMESTAMP
);
```

### Updated Tables

#### `seller` - Added Cashfree Beneficiary Fields

- `cashfreeBeneficiaryId` - Cashfree beneficiary ID
- `beneficiaryStatus` - Verification status
- `beneficiaryCreatedAt` - Creation timestamp
- `beneficiaryUpdatedAt` - Update timestamp

#### `kabadiwala` - Added Cashfree Beneficiary Fields

- `cashfreeBeneficiaryId` - Cashfree beneficiary ID
- `beneficiaryStatus` - Verification status
- `beneficiaryCreatedAt` - Creation timestamp
- `beneficiaryUpdatedAt` - Update timestamp

#### `scraphub` - Added Cashfree Beneficiary Fields

- `cashfreeBeneficiaryId` - Cashfree beneficiary ID
- `beneficiaryStatus` - Verification status
- `beneficiaryCreatedAt` - Creation timestamp
- `beneficiaryUpdatedAt` - Update timestamp

#### `systemConfiguration` - Added New Config

- `SCRAPHUB_MINIMUM_WALLET_BALANCE` - Minimum balance requirement (₹2,00,000)

## 🏗️ **App-Specific Implementations**

### 1. **scraplo-web** - Seller Management

#### Features Implemented:

- ✅ **Beneficiary Creation**: During seller onboarding (Step 3)
- ✅ **V2 API Integration**: Updated to use Cashfree API V2
- ✅ **Validation**: Client-side validation before API calls
- ✅ **Payout History**: Seller can view payout history
- ✅ **Status Tracking**: Real-time payout status updates

#### Key Files:

- `apps/scraplo-web/src/server/api/router/payment.ts` - Updated beneficiary creation
- `apps/scraplo-web/src/server/api/router/seller-payout.ts` - New payout management

#### API Endpoints:

- `sellerPayout.getPayoutHistory` - Get seller payout history
- `sellerPayout.getPayoutStatus` - Get specific payout status
- `sellerPayout.createSellerPayout` - Internal payout creation
- `sellerPayout.getPayoutSummary` - Payout statistics

### 2. **kabadiwala-mobile** - Wallet & Payout Management

#### Features Implemented:

- ✅ **Beneficiary Setup**: Create withdrawal accounts
- ✅ **Wallet Withdrawals**: Withdraw from wallet balance
- ✅ **Seller Payout Triggers**: Trigger seller payouts on order completion
- ✅ **V2 API Integration**: Updated to use Cashfree API V2
- ✅ **Balance Validation**: Ensure sufficient balance before withdrawals

#### Key Files:

- `packages/kabadiwala-api/src/router/payout.ts` - Updated payout router

#### API Endpoints:

- `createKabadiwalaWithdrawalAccount` - Setup withdrawal account
- `createPayout` - Wallet withdrawal (updated to V2)
- `triggerSellerPayout` - Trigger seller payouts
- `getWalletBalance` - Get current wallet balance

### 3. **scraphub** - Business Wallet Management

#### Features Implemented:

- ✅ **Minimum Balance Enforcement**: ₹2,00,000 minimum balance
- ✅ **Withdrawal Account Setup**: Bank account/UPI beneficiary creation
- ✅ **Wallet Withdrawals**: Withdraw available balance
- ✅ **Admin Approval Check**: Only approved ScrapHubs can withdraw
- ✅ **Config-Based Limits**: Minimum balance from system configuration

#### Key Files:

- `apps/scraphub/src/server/api/router/payout.ts` - Updated payout router

#### API Endpoints:

- `getWalletBalance` - Enhanced with minimum balance info
- `createWithdrawalAccount` - Setup withdrawal account
- `createWithdrawal` - Process withdrawal requests

### 4. **admin** - Centralized Management & Webhooks

#### Features Implemented:

- ✅ **Unified Payment & Payout Dashboard**: View all transactions across user types
- ✅ **Comprehensive Webhook Integration**: Real-time status updates for both payments and payouts
- ✅ **Payment & Payout Statistics**: Comprehensive analytics and reporting
- ✅ **Manual Intervention**: Update transaction statuses manually
- ✅ **Bulk Operations**: Bulk status updates
- ✅ **Failed Transaction Management**: Special handling for failed payments and payouts

#### Key Files:

- `apps/admin/src/server/api/router/payout.ts` - Payout management
- `apps/admin/src/server/api/router/webhook.ts` - Webhook monitoring (TRPC)
- `apps/admin/src/app/api/webhooks/cashfree/route.ts` - Centralized webhook endpoint (App Router)

#### API Endpoints:

- `payout.getAllPayouts` - Get all payouts with filtering
- `payout.getPayoutStats` - Comprehensive statistics
- `payout.getFailedPayouts` - Failed payouts needing attention
- `payout.updatePayoutStatus` - Manual status updates
- `payout.bulkUpdatePayouts` - Bulk operations
- `POST /api/webhooks/cashfree` - Centralized webhook endpoint (all payments & transfers, all user types)

## 🔄 **Payment & Payout Flow Diagrams**

### Payment Flows (Collections)

#### Kabadiwala Wallet Top-up

```
Kabadiwala → Wallet Top-up → Cashfree Payment Gateway → Payment Success → Webhook → Update Wallet Balance
```

#### ScrapHub Wallet Deposit

```
ScrapHub → Wallet Deposit → Cashfree Payment Gateway → Payment Success → Webhook → Update Wallet Balance
```

#### Customer Order Payment

```
Customer → Order Payment → Cashfree Payment Gateway → Payment Success → Webhook → Update Order Status
```

### Payout Flows (Transfers)

### Seller Payout Flow (Triggered by Kabadiwala)

```
Order Completion → Kabadiwala App → triggerSellerPayout → Cashfree API → Seller Bank Account
                                                      ↓
                                              Payout Table Record
                                                      ↓
                                              Webhook Updates Status
```

### Kabadiwala Withdrawal Flow

```
Kabadiwala → createPayout → Validate Balance → Cashfree API → Kabadiwala Bank Account
                                    ↓                ↓
                            Deduct from Wallet → Payout Record → Webhook Updates
```

### ScrapHub Withdrawal Flow

```
ScrapHub → createWithdrawal → Check Min Balance → Cashfree API → ScrapHub Bank Account
                                     ↓                 ↓
                             Validate Approval → Payout Record → Webhook Updates
```

## 🔐 **Security & Validation**

### Input Validation

- ✅ **Beneficiary ID**: Max 50 chars, alphanumeric + underscore/pipe/dot
- ✅ **IFSC Codes**: 11-character format validation
- ✅ **Bank Accounts**: 4-25 alphanumeric characters
- ✅ **UPI Addresses**: Valid VPA format
- ✅ **Phone Numbers**: Indian format (8-12 digits)
- ✅ **Transfer Amounts**: Minimum ₹1.00

### Webhook Security & Implementation

- ✅ **Signature Verification**: HMAC-SHA256 signature validation using Cashfree secret key
- ✅ **Timestamp Validation**: Prevent replay attacks using `x-webhook-timestamp` header
- ✅ **Payload Validation**: Schema validation for webhook data
- ✅ **App Router Implementation**: Modern Next.js App Router approach
- ✅ **Centralized Processing**: Single webhook endpoint handles all user types
- ✅ **Error Handling**: Proper HTTP status codes for webhook retry logic

### Centralized Webhook Endpoint

- **Admin App**: `/api/webhooks/cashfree` - Handles ALL payment & transfer webhooks for all user types
  - **Payments**: Kabadiwala wallet top-ups, ScrapHub deposits, customer order payments
  - **Payouts**: Seller payouts, kabadiwala withdrawals, and scraphub withdrawals
  - User-type specific logic within single endpoint
  - Centralized monitoring and logging for both payment flows

## 📈 **Monitoring & Analytics**

### Admin Dashboard Features

- **Real-time Statistics**: Success/failure rates, amounts, counts
- **User Type Breakdown**: Separate stats for sellers, kabadiwalas, scraphubs
- **Status Monitoring**: Track pending, failed, successful payouts
- **Webhook Health**: Monitor webhook processing success rates

### Error Handling

- **Graceful Degradation**: API failures don't break user experience
- **Retry Logic**: Webhook retries for server errors
- **Comprehensive Logging**: Detailed error logs for debugging
- **Status Synchronization**: Periodic status sync from Cashfree

## 🚀 **Deployment Checklist**

### Environment Variables Required

```bash
# Cashfree Configuration
CASHFREE_APP_ID=your_app_id
CASHFREE_SECRET_KEY=your_secret_key
NEXT_PUBLIC_CASHFREE_ENVIRONMENT=sandbox|production
CASHFREE_WEBHOOK_SECRET=your_webhook_secret
```

### Database Migration

1. Run database migrations to add new tables and columns
2. Seed system configuration with minimum balance setting
3. Update existing beneficiary records if needed

### Webhook Configuration

1. Configure single webhook URL in Cashfree dashboard:
   - **Centralized**: `https://admin.yourdomain.com/api/webhooks/cashfree` (all payments & transfers, all user types)
2. Enable payment webhooks:
   - `PAYMENT_SUCCESS_WEBHOOK` - Payment completed successfully
   - `PAYMENT_FAILED_WEBHOOK` - Payment failed
   - `PAYMENT_USER_DROPPED_WEBHOOK` - User abandoned payment
3. Enable transfer status webhooks:
   - `TRANSFER_SUCCESS` - Payout completed successfully
   - `TRANSFER_FAILED` - Payout failed
   - `TRANSFER_REVERSED` - Payout reversed
4. Enable beneficiary webhooks (optional):
   - `BENEFICIARY_VERIFIED` - Beneficiary verified
   - `BENEFICIARY_FAILED` - Beneficiary verification failed
5. Enable settlement webhooks (optional):
   - `SETTLEMENT_SUCCESS` - Settlement completed
   - `SETTLEMENT_FAILED` - Settlement failed
6. Set webhook secret for signature verification
7. Use proper headers: `x-webhook-signature` and `x-webhook-timestamp`

### Testing Checklist

- [ ] Seller beneficiary creation during onboarding
- [ ] Kabadiwala withdrawal account setup
- [ ] ScrapHub withdrawal with minimum balance check
- [ ] Seller payout triggering from kabadiwala app
- [ ] Webhook processing and status updates
- [ ] Admin dashboard payout management
- [ ] Error handling and validation

## 🎉 **Success Metrics**

The integration provides:

- **Unified Payout System**: Single source of truth for all payouts
- **Centralized Webhook Processing**: Single endpoint handles all user types
- **Real-time Status Updates**: Webhook-driven status synchronization
- **Comprehensive Validation**: Client-side and server-side validation
- **Scalable Architecture**: Supports multiple user types and payout scenarios
- **Admin Visibility**: Complete oversight of all payout operations
- **Business Rule Enforcement**: Minimum balances, approval requirements
- **Audit Trail**: Complete transaction history and metadata
- **Modern App Router**: Next.js 13+ App Router implementation
- **Simplified Maintenance**: Single webhook endpoint to maintain

## 📞 **Support & Maintenance**

### Monitoring Points

- Webhook processing success rates
- Payout failure rates by user type
- API response times and error rates
- Balance validation accuracy

### Common Issues & Solutions

- **Beneficiary Verification Failures**: Check bank details, IFSC codes
- **Webhook Processing Errors**: Verify signature and payload format
- **Balance Validation Issues**: Check minimum balance configuration
- **Status Sync Problems**: Manual status updates via admin panel

The Cashfree payout integration is now production-ready with comprehensive error handling, monitoring, and administrative controls! 🚀
