import type { BetterAuthOptions } from "better-auth";
import bcrypt from "bcryptjs";
import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { customSession } from "better-auth/plugins";

import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import { scraphubEmployee } from "@acme/db/schema";
import { sendEmail } from "@acme/mail";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../env";

export const config = {
  database: drizzleAdapter(db, {
    provider: "pg",
  }),
  user: {
    modelName: "scraphubEmployee",
  },
  account: {
    modelName: "scraphubEmployeeAccount",
    fields: {
      userId: "scraphubEmployeeId",
    },
  },
  session: {
    modelName: "scraphubEmployeeSession",
    fields: {
      userId: "scraphubEmployeeId",
    },
  },
  verification: {
    modelName: "scraphubEmployeeVerification",
  },
  emailAndPassword: {
    enabled: true,
    password: {
      hash: async (password) => {
        return bcrypt.hash(password, 10);
      },
      verify: async ({ hash, password }) => {
        return bcrypt.compare(password, hash);
      },
    },
    sendResetPassword: async ({ user, url, token }) => {
      const res = await sendEmail({
        to: user.email,
        subject: "Reset Password",
        content: `<div>
                    <h1>Reset Password</h1>
                    <p>Click <a href="${url}?token=${token}">here</a> to reset your password</p>
                    <p>Or copy and paste the link below into your browser: ${url}?token=${token}</p>
                </div>`,
      });
      console.log("mail response is: ", res);
    },
  },
  baseURL: env.NEXT_PUBLIC_SCRAPHUB_EMPLOYEE_BETTER_AUTH_URL,
  secret: env.SCRAPHUB_EMPLOYEE_BETTER_AUTH_SECRET,
  trustedOrigins: ["exp://"],
  plugins: [
    customSession(async ({ user, session }) => {
      const { data, err } = await tryCatch(
        db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, user.id),
          columns: {
            role: true,
          },
        }),
      );

      if (err) {
        return Promise.reject(new Error("Failed to login user"));
      }

      return {
        user: {
          ...user,
          role: data?.role,
        },
        session,
      };
    }),
  ],
} satisfies BetterAuthOptions;

export const auth = betterAuth(config);
export type Session = typeof auth.$Infer.Session;
