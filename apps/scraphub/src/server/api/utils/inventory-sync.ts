import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import {
  order,
  scraphubInventory,
  scraphubInventoryItem,
} from "@acme/db/schema";

/**
 * Sync inventory for a specific scraphub by processing all orders with "WITH_SCRAPHUB" status
 */
export async function syncScraphubInventory(scraphubId: string): Promise<{
  itemsAdded: number;
  totalValue: number;
  totalItems: number;
  ordersProcessed: number;
}> {
  return await db.transaction(async (tx) => {
    // Get or create inventory record
    let inventoryRecord = await tx.query.scraphubInventory.findFirst({
      where: eq(scraphubInventory.scraphubId, scraphubId),
    });

    if (!inventoryRecord) {
      const [newInventory] = await tx
        .insert(scraphubInventory)
        .values({
          scraphubId,
          totalValue: "0",
          totalItems: "0",
        })
        .returning();
      inventoryRecord = newInventory!;
    }

    // Find all orders with afterCompletedStatus = "WITH_SCRAPHUB"
    const ordersWithScraphub = await tx.query.order.findMany({
      where: eq(order.afterCompletedStatus, "WITH_SCRAPHUB"),
      with: {
        items: {
          with: {
            category: {
              columns: {
                id: true,
                name: true,
                rate: true,
              },
            },
          },
        },
      },
    });

    // Clear existing inventory items for this scraphub
    await tx
      .delete(scraphubInventoryItem)
      .where(eq(scraphubInventoryItem.inventoryId, inventoryRecord.id));

    let totalValue = 0;
    let totalItems = 0;
    let itemsAdded = 0;

    // Add all items from orders with WITH_SCRAPHUB status
    for (const orderRecord of ordersWithScraphub) {
      for (const item of orderRecord.items) {
        if (
          item.quantityAtScraphub &&
          parseFloat(item.quantityAtScraphub) > 0
        ) {
          const quantity = parseFloat(item.quantityAtScraphub);
          const rate = parseFloat(item.rate);
          const itemTotalValue = quantity * rate;

          await tx.insert(scraphubInventoryItem).values({
            inventoryId: inventoryRecord.id,
            categoryId: item.categoryId,
            orderId: orderRecord.id,
            orderItemId: item.id,
            quantity: item.quantityAtScraphub,
            rate: item.rate,
            totalValue: itemTotalValue.toString(),
          });

          totalValue += itemTotalValue;
          totalItems += quantity;
          itemsAdded++;
        }
      }
    }

    // Update inventory summary
    await tx
      .update(scraphubInventory)
      .set({
        totalValue: totalValue.toString(),
        totalItems: totalItems.toString(),
        lastUpdatedAt: new Date(),
      })
      .where(eq(scraphubInventory.id, inventoryRecord.id));

    return {
      itemsAdded,
      totalValue,
      totalItems,
      ordersProcessed: ordersWithScraphub.length,
    };
  });
}

/**
 * Add a single order to inventory when it's marked as "WITH_SCRAPHUB"
 */
export async function addOrderToInventory(
  scraphubId: string,
  orderId: string,
): Promise<void> {
  await db.transaction(async (tx) => {
    // Get or create inventory record
    let inventoryRecord = await tx.query.scraphubInventory.findFirst({
      where: eq(scraphubInventory.scraphubId, scraphubId),
    });

    if (!inventoryRecord) {
      const [newInventory] = await tx
        .insert(scraphubInventory)
        .values({
          scraphubId,
          totalValue: "0",
          totalItems: "0",
        })
        .returning();
      inventoryRecord = newInventory!;
    }

    // Get the order with its items
    const orderRecord = await tx.query.order.findFirst({
      where: eq(order.id, orderId),
      with: {
        items: true,
      },
    });

    if (!orderRecord) {
      throw new Error(`Order ${orderId} not found`);
    }

    let addedValue = 0;
    let addedItems = 0;

    // Add items from this order to inventory
    for (const item of orderRecord.items) {
      if (item.quantityAtScraphub && parseFloat(item.quantityAtScraphub) > 0) {
        const quantity = parseFloat(item.quantityAtScraphub);
        const rate = parseFloat(item.rate);
        const itemTotalValue = quantity * rate;

        // Check if this item is already in inventory (avoid duplicates)
        const existingItem = await tx.query.scraphubInventoryItem.findFirst({
          where: eq(scraphubInventoryItem.orderItemId, item.id),
        });

        if (!existingItem) {
          await tx.insert(scraphubInventoryItem).values({
            inventoryId: inventoryRecord.id,
            categoryId: item.categoryId,
            orderId: orderRecord.id,
            orderItemId: item.id,
            quantity: item.quantityAtScraphub,
            rate: item.rate,
            totalValue: itemTotalValue.toString(),
          });

          addedValue += itemTotalValue;
          addedItems += quantity;
        }
      }
    }

    // Update inventory totals
    const currentTotalValue = parseFloat(inventoryRecord.totalValue);
    const currentTotalItems = parseFloat(inventoryRecord.totalItems);

    await tx
      .update(scraphubInventory)
      .set({
        totalValue: (currentTotalValue + addedValue).toString(),
        totalItems: (currentTotalItems + addedItems).toString(),
        lastUpdatedAt: new Date(),
      })
      .where(eq(scraphubInventory.id, inventoryRecord.id));
  });
}
