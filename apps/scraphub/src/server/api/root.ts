import { adminRouter } from "./router/admin";
import { authRouter } from "./router/auth";
import { inventoryRouter } from "./router/inventory";
import { orderRouter } from "./router/order";
import { paymentRouter } from "./router/payment";
import { payoutRouter } from "./router/payout";
import { profileRouter } from "./router/profile";
import { utilsRouter } from "./router/utils";
import { createTRPCRouter } from "./trpc";

export const appRouter = createTRPCRouter({
  profile: profileRouter,
  order: orderRouter,
  payment: paymentRouter,
  payout: payoutRouter,
  auth: authRouter,
  utils: utilsRouter,
  inventory: inventoryRouter,
  admin: adminRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;
