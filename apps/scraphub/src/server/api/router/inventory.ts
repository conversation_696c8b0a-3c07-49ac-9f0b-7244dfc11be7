import { TRPCError } from "@trpc/server";
import { and, eq, sql } from "drizzle-orm";
import { z } from "zod";

import {
  category,
  scraphubEmployee,
  scraphubInventory,
  scraphubInventoryItem,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { createTRPCRouter, protectedProcedure } from "../trpc";
import { syncScraphubInventory } from "../utils/inventory-sync";

export const inventoryRouter = createTRPCRouter({
  // Get current inventory summary for the scraphub
  getInventorySummary: protectedProcedure.query(async ({ ctx }) => {
    const { data: employeeData, err: employeeErr } = await tryCatch(
      ctx.db.query.scraphubEmployee.findFirst({
        where: eq(scraphubEmployee.id, ctx.session.user.id!),
        with: {
          scraphub: {
            columns: {
              id: true,
              name: true,
            },
          },
        },
      }),
    );

    if (employeeErr || !employeeData || !employeeData.scraphub) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "ScrapHub not found",
      });
    }

    // Get or create inventory record
    const { data: inventoryData, err: inventoryErr } = await tryCatch(
      ctx.db.query.scraphubInventory.findFirst({
        where: eq(scraphubInventory.scraphubId, employeeData.scraphub.id),
      }),
    );

    if (inventoryErr) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch inventory",
      });
    }

    // If no inventory exists, create one
    if (!inventoryData) {
      const { data: newInventory, err: createErr } = await tryCatch(
        ctx.db
          .insert(scraphubInventory)
          .values({
            scraphubId: employeeData.scraphub.id,
            totalValue: "0",
            totalItems: "0",
          })
          .returning(),
      );

      if (createErr || !newInventory[0]) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create inventory",
        });
      }

      return {
        id: newInventory[0].id,
        totalValue: "0",
        totalItems: "0",
        lastUpdatedAt: newInventory[0].lastUpdatedAt,
        scraphubName: employeeData.scraphub.name,
      };
    }

    return {
      id: inventoryData.id,
      totalValue: inventoryData.totalValue,
      totalItems: inventoryData.totalItems,
      lastUpdatedAt: inventoryData.lastUpdatedAt,
      scraphubName: employeeData.scraphub.name,
    };
  }),

  // Get detailed inventory items with category information
  getInventoryItems: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(20),
        categoryId: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: employeeData, err: employeeErr } = await tryCatch(
        ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, ctx.session.user.id!),
          with: {
            scraphub: {
              columns: {
                id: true,
              },
            },
          },
        }),
      );

      if (employeeErr || !employeeData || !employeeData.scraphub) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "ScrapHub not found",
        });
      }

      // Get inventory
      const { data: inventoryData, err: inventoryErr } = await tryCatch(
        ctx.db.query.scraphubInventory.findFirst({
          where: eq(scraphubInventory.scraphubId, employeeData.scraphub.id),
        }),
      );

      if (inventoryErr || !inventoryData) {
        return {
          items: [],
          totalCount: 0,
          totalPages: 0,
          currentPage: input.page,
        };
      }

      const offset = (input.page - 1) * input.limit;

      // Build where conditions
      const whereConditions = [
        eq(scraphubInventoryItem.inventoryId, inventoryData.id),
      ];

      if (input.categoryId) {
        whereConditions.push(
          eq(scraphubInventoryItem.categoryId, input.categoryId),
        );
      }

      // Get inventory items with category and order information
      const { data: items, err: itemsErr } = await tryCatch(
        ctx.db.query.scraphubInventoryItem.findMany({
          where: and(...whereConditions),
          with: {
            category: {
              columns: {
                id: true,
                name: true,
                rateType: true,
                image: true,
              },
            },
            order: {
              columns: {
                id: true,
                createdAt: true,
              },
            },
          },
          limit: input.limit,
          offset: offset,
          orderBy: (table, { desc }) => [desc(table.addedAt)],
        }),
      );

      if (itemsErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch inventory items",
        });
      }

      // Get total count
      const { data: countResult, err: countErr } = await tryCatch(
        ctx.db
          .select({ count: sql<number>`count(*)` })
          .from(scraphubInventoryItem)
          .where(and(...whereConditions)),
      );

      if (countErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to count inventory items",
        });
      }

      const totalCount = countResult[0]?.count ?? 0;
      const totalPages = Math.ceil(totalCount / input.limit);

      return {
        items: items || [],
        totalCount,
        totalPages,
        currentPage: input.page,
      };
    }),

  // Get inventory by category summary
  getInventoryByCategory: protectedProcedure.query(async ({ ctx }) => {
    const { data: employeeData, err: employeeErr } = await tryCatch(
      ctx.db.query.scraphubEmployee.findFirst({
        where: eq(scraphubEmployee.id, ctx.session.user.id!),
        with: {
          scraphub: {
            columns: {
              id: true,
            },
          },
        },
      }),
    );

    if (employeeErr || !employeeData || !employeeData.scraphub) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "ScrapHub not found",
      });
    }

    // Get inventory
    const { data: inventoryData, err: inventoryErr } = await tryCatch(
      ctx.db.query.scraphubInventory.findFirst({
        where: eq(scraphubInventory.scraphubId, employeeData.scraphub.id),
      }),
    );

    if (inventoryErr || !inventoryData) {
      return [];
    }

    // Get category-wise summary
    const { data: categorySummary, err: summaryErr } = await tryCatch(
      ctx.db
        .select({
          categoryId: scraphubInventoryItem.categoryId,
          categoryName: category.name,
          categoryImage: category.image,
          rateType: category.rateType,
          totalQuantity: sql<string>`sum(${scraphubInventoryItem.quantity})`,
          totalValue: sql<string>`sum(${scraphubInventoryItem.totalValue})`,
          itemCount: sql<number>`count(*)`,
        })
        .from(scraphubInventoryItem)
        .innerJoin(category, eq(scraphubInventoryItem.categoryId, category.id))
        .where(eq(scraphubInventoryItem.inventoryId, inventoryData.id))
        .groupBy(
          scraphubInventoryItem.categoryId,
          category.name,
          category.image,
          category.rateType,
        )
        .orderBy(sql`sum(${scraphubInventoryItem.totalValue}) desc`),
    );

    if (summaryErr) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch category summary",
      });
    }

    return categorySummary || [];
  }),

  // Sync inventory from orders (manual trigger for now)
  syncInventory: protectedProcedure.mutation(async ({ ctx }) => {
    const { data: employeeData, err: employeeErr } = await tryCatch(
      ctx.db.query.scraphubEmployee.findFirst({
        where: eq(scraphubEmployee.id, ctx.session.user.id!),
        with: {
          scraphub: {
            columns: {
              id: true,
            },
          },
        },
      }),
    );

    if (employeeErr || !employeeData || !employeeData.scraphub) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "ScrapHub not found",
      });
    }

    const scraphubId = employeeData.scraphub.id;

    // Use the utility function to sync inventory
    const { data: syncResult, err: syncErr } = await tryCatch(
      syncScraphubInventory(scraphubId),
    );

    if (syncErr) {
      console.error("Failed to sync inventory:", syncErr);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to sync inventory",
      });
    }

    return {
      message: "Inventory sync completed successfully",
      scraphubId,
      ...syncResult,
    };
  }),
});
