import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { Cashfree } from "@acme/cashfree-sdk";
import { eq, sql } from "@acme/db";
import {
  scraphub,
  scraphubEmployee,
  scraphubPaymentTransaction,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { env } from "~/env";
import analytics from "~/lib/analytics";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Initialize Cashfree client
const cashfree = new Cashfree({
  appId: env.CASHFREE_APP_ID,
  secretKey: env.CASHFREE_SECRET_KEY,
  environment: env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT,
});

// Minimum deposit amount (2,00,000 INR)
const MINIMUM_DEPOSIT_AMOUNT = 200000;

const DepositSchema = z.object({
  amount: z
    .number()
    .min(
      MINIMUM_DEPOSIT_AMOUNT,
      `Minimum deposit amount is ₹${MINIMUM_DEPOSIT_AMOUNT.toLocaleString()}`,
    ),
  currency: z.string().default("INR"),
});

const VerifyDepositSchema = z.object({
  orderId: z.string(),
  // paymentId: z.string().optional(),
  // signature: z.string().optional(),
  // paymentGateway: z.enum(["RAZORPAY", "CASHFREE"]).default("CASHFREE"),
});

export const paymentRouter = createTRPCRouter({
  // Get current wallet balance and approval status
  getWalletInfo: protectedProcedure.query(async ({ ctx }) => {
    const { data: scraphubData, err } = await tryCatch(
      ctx.db.query.scraphubEmployee.findFirst({
        where: eq(scraphubEmployee.id, ctx.session.user.id!),
        with: {
          scraphub: {
            columns: {
              walletBalance: true,
              adminApprovalStatus: true,
              adminApprovedAt: true,
              adminRejectionReason: true,
            },
          },
        },
      }),
    );

    if (err || !scraphubData?.scraphub) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "ScrapHub not found",
      });
    }

    return {
      walletBalance: Number(scraphubData.scraphub.walletBalance),
      adminApprovalStatus: scraphubData.scraphub.adminApprovalStatus,
      adminApprovedAt: scraphubData.scraphub.adminApprovedAt,
      adminRejectionReason: scraphubData.scraphub.adminRejectionReason,
      canDeposit: scraphubData.scraphub.adminApprovalStatus === "APPROVED",
      minimumDepositAmount: MINIMUM_DEPOSIT_AMOUNT,
    };
  }),

  // Create deposit order
  createDepositOrder: protectedProcedure
    .input(DepositSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if ScrapHub is approved
      const { data: scraphubData, err: scraphubErr } = await tryCatch(
        ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, ctx.session.user.id!),
          with: {
            scraphub: {
              columns: {
                adminApprovalStatus: true,
                name: true,
                id: true,
                phoneNumber: true,
              },
            },
          },
        }),
      );

      if (scraphubErr || !scraphubData?.scraphub) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "ScrapHub not found",
        });
      }

      if (scraphubData.scraphub.adminApprovalStatus !== "APPROVED") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message:
            "Your account must be approved by admin before making deposits",
        });
      }

      // Create Cashfree order directly
      const cashfreeOrderId = `deposit_${Date.now()}`;
      const customerId = `scraphub_${scraphubData.scraphub.id}`;

      const orderRequest = {
        order_id: cashfreeOrderId,
        order_amount: input.amount,
        order_currency: input.currency,
        customer_details: {
          customer_id: customerId,
          customer_name: scraphubData.scraphub.name,
          customer_email: ctx.session.user.email ?? "",
          customer_phone: scraphubData.scraphub.phoneNumber ?? "",
        },
        order_note: `Deposit of ₹${input.amount.toLocaleString()}`,
        order_tags: {
          scraphub_id: scraphubData.scraphub.id,
          scraphub_employee_id: ctx.session.user.id ?? "",
          transaction_for: "DEPOSIT",
        },
      };

      const { data: orderData, error: orderError } =
        await cashfree.createOrder(orderRequest);

      if (orderError || !orderData) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create deposit order",
        });
      }

      console.log("orderData", orderData);

      // Prepare transaction data for Cashfree
      const transactionData = {
        scraphubId: scraphubData.scraphub.id,
        amount: input.amount.toString(),
        paymentGateway: "CASHFREE" as const,
        status: "PENDING" as const,
        currency: input.currency,
        transactionFor: "DEPOSIT" as const,
        transactionType: "CREDIT" as const,
        description: `Deposit of ₹${input.amount.toLocaleString()}`,
        razorpayOrderId: "",
        cashfreeOrderId: cashfreeOrderId,
        gatewayMetadata: {
          order_created_at: new Date().toISOString(),
          gateway: "CASHFREE",
          order_amount: orderData.order_amount,
          order_currency: orderData.order_currency,
          order_status: orderData.order_status,
          cashfree_order_id: cashfreeOrderId,
          cf_order_id: orderData.cf_order_id,
          // order_token: orderData.order_token,
          ...(orderData.order_note && { order_note: orderData.order_note }),
          ...(orderData.order_tags && { order_tags: orderData.order_tags }),
        },
      };

      const { data: insertData, err: insertErr } = await tryCatch(
        ctx.db
          .insert(scraphubPaymentTransaction)
          .values(transactionData)
          .returning(),
      );

      if (insertErr || !insertData?.[0]) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create transaction record",
        });
      }

      const transactionId = insertData[0].id;

      // Track deposit initiation
      analytics.trackEvent("wallet_deposit_initiated", {
        user_id: ctx.session.user.id,
        scraphub_id: scraphubData.scraphub.id,
        user_type: "scraphub_employee",
        amount: input.amount,
        currency: input.currency,
        gateway: "CASHFREE",
        transaction_id: transactionId,
      });

      return {
        orderId: cashfreeOrderId,
        transactionId: transactionId,
        name: "ScrapHub Deposit",
        amount: (orderData.order_amount ?? input.amount) * 100, // Convert to paise for frontend compatibility
        currency: orderData.order_currency ?? input.currency,
        description: `Deposit of ₹${input.amount.toLocaleString()} to ScrapHub wallet`,
        gateway: "CASHFREE",
        payment_session_id: orderData.payment_session_id,
      };
    }),

  // Verify deposit payment
  verifyDeposit: protectedProcedure
    .input(VerifyDepositSchema)
    .mutation(async ({ ctx, input }) => {
      const { orderId } = input;

      // Get transaction details to determine gateway
      const { data: transaction, err: transactionErr } = await tryCatch(
        ctx.db.query.scraphubPaymentTransaction.findFirst({
          where: eq(scraphubPaymentTransaction.cashfreeOrderId, orderId),
        }),
      );

      if (transactionErr || !transaction) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Transaction not found",
        });
      }

      ctx.db
        .transaction(async (tx) => {
          // Prepare base metadata
          const baseMetadata = {
            verified_at: new Date().toISOString(),
            payment_method: transaction.paymentGateway,
            verification_successful: true,
          };

          // Merge existing metadata with new verification data
          const existingMetadata = transaction.gatewayMetadata ?? {};
          const newMetadata = {
            ...existingMetadata,
            ...baseMetadata,
          };

          // Update transaction status
          const updateData = {
            status: "SUCCESS" as const,
            gatewayMetadata: newMetadata,
          };

          // updateData.gatewayMetadata.cf_payment_id = input.paymentId; // TODO: Remove this after testing

          await tx
            .update(scraphubPaymentTransaction)
            .set(updateData)
            .where(eq(scraphubPaymentTransaction.id, transaction.id));

          await tx
            .update(scraphub)
            .set({
              walletBalance: sql`${scraphub.walletBalance} + ${transaction.amount}`,
            })
            .where(eq(scraphub.id, transaction.scraphubId));
        })
        .catch((err) => {
          console.error("Error verifying deposit:", err);
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to verify deposit",
          });
        });

      // Track successful deposit completion
      analytics.trackEvent("wallet_deposit_completed", {
        user_id: ctx.session.user.id,
        scraphub_id: transaction.scraphubId,
        user_type: "scraphub",
        amount: Number(transaction.amount),
        currency: transaction.currency ?? "INR",
        gateway: transaction.paymentGateway,
        transaction_id: transaction.id,
      });

      // Track wallet balance update
      analytics.trackWalletTransaction({
        user_id: ctx.session.user.id ?? "",
        // scraphub_id: transaction.scraphubId,
        user_type: "scraphub",
        transaction_type: "CREDIT",
        amount: Number(transaction.amount),
        currency: transaction.currency ?? "INR",
        balance_before: 0, // Would need to fetch previous balance
        balance_after: Number(transaction.amount), // Would need to fetch new balance
        transaction_for: "DEPOSIT",
      });

      return {
        success: true,
        message: "Deposit verified successfully",
        amount: Number(transaction.amount),
      };
    }),

  // Get transaction history
  getTransactionHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: transactions, err } = await tryCatch(
        ctx.db.query.scraphubPaymentTransaction.findMany({
          where: eq(
            scraphubPaymentTransaction.scraphubId,
            ctx.session.user.scraphubId!,
          ),
          orderBy: (transactions, { desc }) => [desc(transactions.createdAt)],
          limit: input.limit,
          offset: input.offset,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch transaction history",
        });
      }

      return {
        transactions:
          transactions?.map((t) => ({
            id: t.id,
            amount: Number(t.amount),
            currency: t.currency,
            status: t.status,
            transactionType: t.transactionType,
            transactionFor: t.transactionFor,
            description: t.description,
            paymentGateway: t.paymentGateway,
            createdAt: t.createdAt,
            updatedAt: t.updatedAt,
          })) ?? [],
      };
    }),

  // Debug endpoint to inspect transaction details
  getTransactionDetails: protectedProcedure
    .input(z.object({ transactionId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: transaction, err: transactionErr } = await tryCatch(
        ctx.db.query.scraphubPaymentTransaction.findFirst({
          where: eq(scraphubPaymentTransaction.id, input.transactionId),
        }),
      );

      if (transactionErr || !transaction) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Transaction not found",
        });
      }

      return {
        transaction,
        debug: {
          hasGatewayMetadata: !!transaction.gatewayMetadata,
          gatewayMetadataKeys: transaction.gatewayMetadata
            ? Object.keys(transaction.gatewayMetadata)
            : [],
          paymentGateway: transaction.paymentGateway,
          status: transaction.status,
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt,
        },
      };
    }),
});
