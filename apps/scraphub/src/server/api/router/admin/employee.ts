import { TRPCError } from "@trpc/server";
import bcrypt from "bcryptjs";
import { eq } from "drizzle-orm";
import { z } from "zod";

import { scraphubEmployee, scraphubEmployeeAccount } from "@acme/db/schema";
import { sendEmail } from "@acme/mail";
import { tryCatch } from "@acme/validators/utils";

import {
  createTRPCRouter,
  scraphubAdminEmployeeProcedure,
} from "~/server/api/trpc";

const EmployeeSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  email: z.string().email({ message: "Valid email is required" }),
  role: z.enum(["ADMIN", "EMPLOYEE"]).default("ADMIN"),
});

const employeeRouter = createTRPCRouter({
  getEmployees: scraphubAdminEmployeeProcedure.query(async ({ ctx }) => {
    const { data: employees, err: employeesErr } = await tryCatch(
      ctx.db.query.scraphubEmployee.findMany({
        where: eq(scraphubEmployee.scraphubId, ctx.session.user.scraphubId!),
      }),
    );

    if (employeesErr) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch employees",
      });
    }

    return employees;
  }),

  getEmployeeById: scraphubAdminEmployeeProcedure
    .input(
      z.object({
        employeeId: z.string().nonempty({ message: "Employee ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: employee, err: employeeErr } = await tryCatch(
        ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, input.employeeId),
        }),
      );

      if (employeeErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch employee",
        });
      }

      if (!employee) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employee not found",
        });
      }

      return employee;
    }),

  createEmployee: scraphubAdminEmployeeProcedure
    .input(EmployeeSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if email already exists
      const { data: existingEmployee, err: existingErr } = await tryCatch(
        ctx.db
          .select({ id: scraphubEmployee.id })
          .from(scraphubEmployee)
          .where(eq(scraphubEmployee.email, input.email)),
      );

      if (existingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing employee",
        });
      }

      if (existingEmployee[0]) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already registered",
        });
      }

      // Generate a random password
      const generatedPassword = Math.random().toString(36).slice(-8);

      // Hash the password
      const hashedPassword = await bcrypt.hash(generatedPassword, 10);

      const { err: txErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          const [scraphubEmployeeRes] = await tx
            .insert(scraphubEmployee)
            .values({
              scraphubId: ctx.session.user.scraphubId!,
              name: input.name,
              email: input.email,
              role: input.role,
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .returning();

          if (!scraphubEmployeeRes) {
            throw new Error("Failed to create scraphub employee");
          }

          await tx.insert(scraphubEmployeeAccount).values({
            scraphubEmployeeId: scraphubEmployeeRes.id,
            accountId: scraphubEmployeeRes.id,
            password: hashedPassword,
            providerId: "credential",
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to create employee",
        });
      }

      await sendEmail({
        to: input.email,
        subject: "Welcome to Scraplo ScrapHub",
        content: `Hello ${input.name},\n\nYour account has been created successfully. Here are your credentials:\n\nEmail: ${input.email}\nPassword: ${generatedPassword}\n\nPlease log in to the portal and change your password.\n\nThank you!`,
      });

      return {
        message: "Employee created successfully. Credentials sent to email.",
      };
    }),

  updateEmployee: scraphubAdminEmployeeProcedure
    .input(
      EmployeeSchema.extend({
        employeeId: z.string().nonempty({ message: "Employee ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Check if email already exists for other employees
      const { data: existingEmployee, err: existingErr } = await tryCatch(
        ctx.db
          .select({ id: scraphubEmployee.id })
          .from(scraphubEmployee)
          .where(eq(scraphubEmployee.email, input.email)),
      );

      if (existingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing employee",
        });
      }

      if (existingEmployee[0] && existingEmployee[0].id !== input.employeeId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already registered",
        });
      }

      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(scraphubEmployee)
          .set({
            name: input.name,
            email: input.email,
            role: input.role,
            updatedAt: new Date(),
          })
          .where(eq(scraphubEmployee.id, input.employeeId)),
      );

      if (updateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: updateErr.message || "Failed to update employee",
        });
      }

      return {
        message: "Employee updated successfully",
      };
    }),

  deleteEmployee: scraphubAdminEmployeeProcedure
    .input(
      z.object({
        employeeId: z.string().nonempty({ message: "Employee ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Check if employee exists and belongs to the same scraphub
      const { data: employee, err: employeeErr } = await tryCatch(
        ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, input.employeeId),
        }),
      );

      if (employeeErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check employee",
        });
      }

      if (!employee) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employee not found",
        });
      }

      if (employee.scraphubId !== ctx.session.user.scraphubId) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Cannot delete employee from different scraphub",
        });
      }

      // Prevent deleting the last admin
      if (employee.role === "ADMIN") {
        const { data: adminCount, err: countErr } = await tryCatch(
          ctx.db
            .select({ count: scraphubEmployee.id })
            .from(scraphubEmployee)
            .where(
              eq(scraphubEmployee.scraphubId, ctx.session.user.scraphubId!),
            ),
        );

        if (countErr) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to check admin count",
          });
        }

        if (adminCount.length <= 1) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Cannot delete the last admin user",
          });
        }
      }

      const { err: deleteErr } = await tryCatch(
        ctx.db
          .delete(scraphubEmployee)
          .where(eq(scraphubEmployee.id, input.employeeId)),
      );

      if (deleteErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: deleteErr.message || "Failed to delete employee",
        });
      }

      return {
        message: "Employee deleted successfully",
      };
    }),
});

export { employeeRouter };
