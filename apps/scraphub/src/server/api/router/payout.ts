import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  Cashfree,
  createBankBeneficiaryRequest,
  createSimpleTransferRequest,
  createTransferId,
  createUPIBeneficiaryRequest,
  validateBeneficiaryRequest,
  validateTransferRequest,
} from "@acme/cashfree-sdk";
import { eq } from "@acme/db";
import {
  payout,
  scraphub,
  scraphubPaymentTransaction,
  systemConfiguration,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../../env";
import analytics from "../../../lib/analytics";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Initialize Cashfree client for payouts
const cashfree = new Cashfree({
  appId: env.CASHFREE_APP_ID,
  secretKey: env.CASHFREE_SECRET_KEY,
  environment:
    (env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT as "sandbox" | "production") ??
    "sandbox",
});

// Schemas
const CreateContactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email().optional(),
  contact: z.string().optional(),
  type: z.enum(["customer", "vendor", "employee"]).default("vendor"),
  reference_id: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

const CreateFundAccountSchema = z
  .object({
    contact_id: z.string(),
    account_type: z.enum(["bank_account", "vpa"]),
    bank_account: z
      .object({
        name: z.string(),
        account_number: z.string(),
        ifsc: z.string(),
      })
      .optional(),
    vpa: z
      .object({
        address: z.string(),
      })
      .optional(),
  })
  .refine(
    (data) => {
      if (data.account_type === "bank_account" && !data.bank_account) {
        return false;
      }
      if (data.account_type === "vpa" && !data.vpa) {
        return false;
      }
      return true;
    },
    {
      message: "Account details must match the account type",
    },
  );

const CreatePayoutSchema = z.object({
  fund_account_id: z.string(),
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().default("INR"),
  mode: z.enum(["IMPS", "NEFT", "RTGS", "UPI"]),
  purpose: z.string(),
  reference_id: z.string().optional(),
  narration: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

const BulkPayoutSchema = z.object({
  payouts: z.array(CreatePayoutSchema),
  reference_id: z.string().optional(),
});

export const payoutRouter = createTRPCRouter({
  // Get payout gateway info
  getGatewayInfo: protectedProcedure.query(async () => {
    return {
      primary: "CASHFREE",
      fallback: null,
      environment: env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT,
    };
  }),

  // Contact management (Cashfree uses beneficiaries instead of contacts)
  createContact: protectedProcedure
    .input(CreateContactSchema)
    .mutation(async ({ input }) => {
      // For Cashfree, we create a beneficiary instead of a contact
      const beneficiaryData = {
        beneficiary_id: `contact_${Date.now()}`,
        beneficiary_name: input.name,
        email: input.email,
        phone: input.contact,
      };

      const { data, error } = await cashfree.createBeneficiary(beneficiaryData);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message ?? "Failed to create beneficiary",
        });
      }

      // Track contact creation
      analytics.trackEvent("feature_used", {
        action: "contact_created",
        contact_type: input.type,
        gateway: "CASHFREE",
      });

      return {
        id: data?.beneficiary_id,
        name: input.name,
        email: input.email,
        contact: input.contact,
      };
    }),

  // Fund account management (Cashfree uses beneficiaries with payment details)
  createFundAccount: protectedProcedure
    .input(CreateFundAccountSchema)
    .mutation(async ({ input }) => {
      // For Cashfree, we create a beneficiary with payment details
      const beneficiaryData: any = {
        bene_id: `fund_${Date.now()}`,
        name: "Fund Account", // This would need to be passed from input or fetched from contact
      };

      // Add payment method details based on account type
      if (input.account_type === "bank_account" && input.bank_account) {
        beneficiaryData.bank_account = input.bank_account.account_number;
        beneficiaryData.ifsc = input.bank_account.ifsc;
      } else if (input.account_type === "vpa" && input.vpa) {
        beneficiaryData.vpa = input.vpa.address;
      }

      const { data, error } = await cashfree.createBeneficiary(beneficiaryData);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message ?? "Failed to create fund account",
        });
      }

      // Track fund account creation
      analytics.trackEvent("feature_used", {
        action: "fund_account_created",
        account_type: input.account_type,
        gateway: "CASHFREE",
      });

      return {
        id: data?.beneficiary_id,
        contact_id: input.contact_id,
        account_type: input.account_type,
        active: true,
      };
    }),

  getAllFundAccounts: protectedProcedure
    .input(z.object({ contact_id: z.string() }))
    .query(async ({ input }) => {
      // For Cashfree, we would list beneficiaries
      // For now, return empty array as we don't have a direct mapping
      return [];
    }),

  deactivateFundAccount: protectedProcedure
    .input(z.object({ fund_account_id: z.string() }))
    .mutation(async ({ input }) => {
      // For Cashfree, we would remove/deactivate a beneficiary
      // For now, return success as we don't have direct mapping
      return { success: true };
    }),

  // Payout operations
  createPayout: protectedProcedure
    .input(CreatePayoutSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if ScrapHub has sufficient balance and is approved
      const { data: scraphubData, err: scraphubErr } = await tryCatch(
        ctx.db.query.scraphub.findFirst({
          where: eq(scraphub.id, ctx.session.user.scraphubId!),
          columns: {
            walletBalance: true,
            adminApprovalStatus: true,
          },
        }),
      );

      if (scraphubErr || !scraphubData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "ScrapHub not found",
        });
      }

      if (scraphubData.adminApprovalStatus !== "APPROVED") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Your account must be approved before making payouts",
        });
      }

      const currentBalance = Number(scraphubData.walletBalance);
      if (currentBalance < input.amount) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Insufficient wallet balance",
        });
      }

      // Track payout initiation
      analytics.trackPayoutInitiated({
        gateway: "CASHFREE",
        amount: input.amount,
        currency: input.currency,
        payout_id: `pending_${Date.now()}`,
        beneficiary_id: input.fund_account_id,
        mode: input.mode,
        purpose: input.purpose,
      });

      // Create Cashfree payout
      const payoutData = {
        transfer_id: `payout_${Date.now()}`,
        transfer_amount: input.amount,
        remarks: input.narration ?? input.purpose,
        beneficiary_details: {
          beneficiary_id: input.fund_account_id,
        },
      };

      const { data, error } = await cashfree.createPayout(payoutData);

      if (error) {
        analytics.trackPayoutFailed({
          gateway: "CASHFREE",
          amount: input.amount,
          currency: input.currency,
          payout_id: `failed_${Date.now()}`,
          beneficiary_id: input.fund_account_id,
          failure_reason: error.message ?? "Payout failed",
        });

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message ?? "Failed to create payout",
        });
      }

      // Record payout transaction
      const { err: insertErr } = await tryCatch(
        ctx.db.insert(scraphubPaymentTransaction).values({
          scraphubId: ctx.session.user.scraphubId!,
          amount: input.amount.toString(),
          paymentGateway: "CASHFREE",
          status: "PENDING",
          currency: input.currency,
          transactionFor: "WITHDRAWAL",
          transactionType: "DEBIT",
          description: `Payout of ₹${input.amount.toLocaleString()} - ${input.purpose}`,
          gatewayMetadata: {
            payout_id: data?.transfer_id,
            fund_account_id: input.fund_account_id,
            purpose: input.purpose,
            mode: input.mode,
          },
        }),
      );

      if (insertErr) {
        console.error("Failed to record payout transaction:", insertErr);
      }

      // Track successful payout creation
      analytics.trackPayoutSuccess({
        gateway: "CASHFREE",
        amount: input.amount,
        currency: input.currency,
        payout_id: data?.transfer_id ?? "unknown",
        beneficiary_id: input.fund_account_id,
      });

      return data;
    }),

  // Bulk payout operations
  createBulkPayout: protectedProcedure
    .input(BulkPayoutSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if ScrapHub has sufficient balance and is approved
      const { data: scraphubData, err: scraphubErr } = await tryCatch(
        ctx.db.query.scraphub.findFirst({
          where: eq(scraphub.id, ctx.session.user.scraphubId!),
          columns: {
            walletBalance: true,
            adminApprovalStatus: true,
          },
        }),
      );

      if (scraphubErr || !scraphubData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "ScrapHub not found",
        });
      }

      if (scraphubData.adminApprovalStatus !== "APPROVED") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Your account must be approved before making payouts",
        });
      }

      const totalAmount = input.payouts.reduce(
        (sum, payout) => sum + payout.amount,
        0,
      );
      const currentBalance = Number(scraphubData.walletBalance);

      if (currentBalance < totalAmount) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Insufficient wallet balance for bulk payout",
        });
      }

      // Create bulk payout with Cashfree
      // For now, we'll create individual payouts since Cashfree bulk API might be different
      const results = [];
      for (const payout of input.payouts) {
        const payoutData = {
          transfer_id: `bulk_payout_${Date.now()}_${Math.random()}`,
          bene_id: payout.fund_account_id,
          transfer_amount: payout.amount,
          remarks: payout.narration ?? payout.purpose,
          beneficiary_details: {
            beneficiary_id: payout.fund_account_id,
          },
        };

        const { data, error } = await cashfree.createPayout(payoutData);
        if (error) {
          console.error("Failed to create payout:", error);
        }
        results.push({ data, error });
      }

      // Track bulk payout
      analytics.trackEvent("feature_used", {
        action: "bulk_payout_created",
        total_amount: totalAmount,
        payout_count: input.payouts.length,
        gateway: "CASHFREE",
      });

      return { results };
    }),

  getPayoutStatus: protectedProcedure
    .input(z.object({ payout_id: z.string() }))
    .query(async ({ input }) => {
      // Get payout status from Cashfree
      const { data, error } = await cashfree.getPayoutStatus(input.payout_id);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message ?? "Failed to get payout status",
        });
      }

      return data;
    }),

  // Validation (Cashfree doesn't have direct fund account validation)
  validateFundAccount: protectedProcedure
    .input(
      z.object({
        fund_account_id: z.string(),
        amount: z.number().positive(),
        currency: z.string().default("INR"),
      }),
    )
    .mutation(async ({ input }) => {
      // For Cashfree, we can't validate fund accounts directly
      // Return success for now
      return {
        success: true,
        message: "Fund account validation not available with Cashfree",
      };
    }),

  // Get ScrapHub wallet balance with minimum balance info
  getWalletBalance: protectedProcedure.query(async ({ ctx }) => {
    const { data: scraphubData, err } = await tryCatch(
      ctx.db.query.scraphub.findFirst({
        where: eq(scraphub.id, ctx.session.user.scraphubId!),
        columns: {
          walletBalance: true,
          adminApprovalStatus: true,
          cashfreeBeneficiaryId: true,
          beneficiaryStatus: true,
        },
      }),
    );

    if (err || !scraphubData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "ScrapHub not found",
      });
    }

    // Get minimum balance requirement from config
    const { data: configData } = await tryCatch(
      ctx.db.query.systemConfiguration.findFirst({
        where: eq(systemConfiguration.key, "SCRAPHUB_MINIMUM_WALLET_BALANCE"),
        columns: { value: true },
      }),
    );

    const minimumBalance = configData?.value
      ? Number(configData.value)
      : 200000; // Default ₹2,00,000
    const currentBalance = Number(scraphubData.walletBalance);
    const availableForWithdrawal = Math.max(0, currentBalance - minimumBalance);

    return {
      balance: currentBalance,
      minimumBalance,
      availableForWithdrawal,
      currency: "INR",
      canPayout: scraphubData.adminApprovalStatus === "APPROVED",
      canWithdraw:
        availableForWithdrawal > 0 && !!scraphubData.cashfreeBeneficiaryId,
      hasWithdrawalAccount: !!scraphubData.cashfreeBeneficiaryId,
      accountStatus: scraphubData.beneficiaryStatus,
    };
  }),

  // Create ScrapHub beneficiary for withdrawals
  createWithdrawalAccount: protectedProcedure
    .input(
      z.object({
        accountType: z.enum(["bank_account", "vpa"]),
        bankAccount: z.string().optional(),
        ifsc: z.string().optional(),
        vpa: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const scraphubId = ctx.session.user.scraphubId!;

      // Get scraphub details
      const { data: scraphubData, err } = await tryCatch(
        ctx.db.query.scraphub.findFirst({
          where: eq(scraphub.id, scraphubId),
          columns: {
            name: true,
            phoneNumber: true,
            cashfreeBeneficiaryId: true,
          },
        }),
      );

      if (err || !scraphubData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "ScrapHub not found",
        });
      }

      // Check if beneficiary already exists
      if (scraphubData.cashfreeBeneficiaryId) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Withdrawal account already exists",
        });
      }

      const beneficiaryId = `scraphub_${scraphubId}`;

      let beneficiaryRequest;
      if (input.accountType === "vpa" && input.vpa) {
        beneficiaryRequest = createUPIBeneficiaryRequest({
          beneficiaryId,
          name: scraphubData.name,
          vpa: input.vpa,
          // email: scraphubData.email ?? undefined,
          phone: scraphubData.phoneNumber ?? undefined,
        });
      } else if (
        input.accountType === "bank_account" &&
        input.bankAccount &&
        input.ifsc
      ) {
        beneficiaryRequest = createBankBeneficiaryRequest({
          beneficiaryId,
          name: scraphubData.name,
          bankAccount: input.bankAccount,
          ifsc: input.ifsc,
          // email: scraphubData.email ?? undefined,
          phone: scraphubData.phoneNumber ?? undefined,
        });
      } else {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid account details provided",
        });
      }

      // Validate request
      const errors = validateBeneficiaryRequest(beneficiaryRequest);
      if (errors.length > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Validation failed: ${errors.join(", ")}`,
        });
      }

      const { data, error } =
        await cashfree.createBeneficiary(beneficiaryRequest);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message ?? "Failed to create withdrawal account",
        });
      }

      // Update scraphub with beneficiary ID
      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(scraphub)
          .set({
            cashfreeBeneficiaryId: data?.beneficiary_id,
            beneficiaryStatus: data?.beneficiary_status,
            beneficiaryCreatedAt: new Date(),
            beneficiaryUpdatedAt: new Date(),
          })
          .where(eq(scraphub.id, scraphubId)),
      );

      if (updateErr) {
        console.error(
          "Failed to update scraphub with beneficiary ID:",
          updateErr,
        );
      }

      return {
        beneficiaryId: data?.beneficiary_id,
        status: data?.beneficiary_status,
        accountType: input.accountType,
      };
    }),

  // Create withdrawal request
  createWithdrawal: protectedProcedure
    .input(
      z.object({
        amount: z.number().min(1),
        remarks: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const scraphubId = ctx.session.user.scraphubId!;

      // Get scraphub details and wallet balance
      const { data: scraphubData, err: scraphubErr } = await tryCatch(
        ctx.db.query.scraphub.findFirst({
          where: eq(scraphub.id, scraphubId),
          columns: {
            walletBalance: true,
            cashfreeBeneficiaryId: true,
            beneficiaryStatus: true,
            name: true,
            adminApprovalStatus: true,
          },
        }),
      );

      if (scraphubErr || !scraphubData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "ScrapHub not found",
        });
      }

      if (scraphubData.adminApprovalStatus !== "APPROVED") {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "ScrapHub not approved for withdrawals",
        });
      }

      if (!scraphubData.cashfreeBeneficiaryId) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Withdrawal account not set up",
        });
      }

      if (scraphubData.beneficiaryStatus !== "VERIFIED") {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Withdrawal account not verified",
        });
      }

      // Get minimum balance requirement
      const { data: configData } = await tryCatch(
        ctx.db.query.systemConfiguration.findFirst({
          where: eq(systemConfiguration.key, "SCRAPHUB_MINIMUM_WALLET_BALANCE"),
          columns: { value: true },
        }),
      );

      const minimumBalance = configData?.value
        ? Number(configData.value)
        : 200000;
      const currentBalance = Number(scraphubData.walletBalance);
      const availableForWithdrawal = currentBalance - minimumBalance;

      if (input.amount > availableForWithdrawal) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Insufficient balance. Available for withdrawal: ₹${availableForWithdrawal}`,
        });
      }

      // Generate unique transfer ID
      const transferId = createTransferId(
        "scraphub_withdrawal",
        `${scraphubId}_${Date.now()}`,
      );

      try {
        const transferRequest = createSimpleTransferRequest({
          transferId,
          amount: input.amount,
          beneficiaryId: scraphubData.cashfreeBeneficiaryId,
          transferMode: "imps",
          remarks:
            input.remarks ?? `Wallet withdrawal for ${scraphubData.name}`,
        });

        // Validate request
        const validationErrors = validateTransferRequest(transferRequest);
        if (validationErrors.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Validation failed: ${validationErrors.join(", ")}`,
          });
        }

        const { data, error } = await cashfree.createTransfer(transferRequest);

        if (error) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to initiate withdrawal",
          });
        }

        // Deduct amount from wallet balance
        const { err: walletUpdateErr } = await tryCatch(
          ctx.db
            .update(scraphub)
            .set({
              walletBalance: (currentBalance - input.amount).toString(),
            })
            .where(eq(scraphub.id, scraphubId)),
        );

        if (walletUpdateErr) {
          console.error("Failed to update wallet balance:", walletUpdateErr);
        }

        // Record payout in unified payout table
        const { err: payoutInsertErr } = await tryCatch(
          ctx.db.insert(payout).values({
            transferId,
            cfTransferId: data?.cf_transfer_id,
            userType: "SCRAPHUB",
            userId: scraphubId,
            beneficiaryId: scraphubData.cashfreeBeneficiaryId,
            amount: input.amount.toString(),
            currency: "INR",
            payoutType: "WALLET_WITHDRAWAL",
            transferMode: "imps",
            status: data?.status ?? "RECEIVED",
            remarks: input.remarks,
            purpose: "Wallet withdrawal",
            gatewayMetadata: {
              minimumBalanceRequired: minimumBalance,
              balanceBeforeWithdrawal: currentBalance,
            },
            createdAt: new Date(),
            updatedAt: new Date(),
          }),
        );

        if (payoutInsertErr) {
          console.error("Failed to record payout:", payoutInsertErr);
        }

        return {
          transferId: data?.transfer_id,
          status: data?.status,
          amount: data?.transfer_amount,
          newBalance: currentBalance - input.amount,
        };
      } catch (error) {
        console.error("ScrapHub withdrawal error:", error);
        throw error;
      }
    }),
});
