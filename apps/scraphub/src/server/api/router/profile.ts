import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { scraphubEmployee } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { createTRPCRouter, protectedProcedure } from "../trpc";

export const profileRouter = createTRPCRouter({
  get: protectedProcedure.query(async ({ ctx }) => {
    const { data: profile, err: profileErr } = await tryCatch(
      ctx.db.query.scraphubEmployee.findFirst({
        where: eq(scraphubEmployee.id, ctx.session.user.id!),
        with: {
          scraphub: {
            columns: {
              name: true,
              phoneNumber: true,
            },
            with: {
              address: {
                columns: {
                  display: true,
                  street: true,
                  city: true,
                  state: true,
                  country: true,
                  postalCode: true,
                  coordinates: true,
                  landmark: true,
                },
              },
            },
          },
        },
      }),
    );

    if (profileErr || !profile) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch profile",
      });
    }

    return profile;
  }),

  update: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, "Name is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: updatedProfile, err: updateErr } = await tryCatch(
        ctx.db
          .update(scraphubEmployee)
          .set({
            name: input.name,
            updatedAt: new Date(),
          })
          .where(eq(scraphubEmployee.id, ctx.session.user.id!))
          .returning(),
      );

      if (updateErr || !updatedProfile[0]) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update profile",
        });
      }

      return updatedProfile[0];
    }),
});
