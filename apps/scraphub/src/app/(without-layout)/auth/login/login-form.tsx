"use client";

import type { z } from "zod";
import { useState } from "react";
import Link from "next/link";
import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { Eye, EyeOff } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { AdminLoginSchema } from "@acme/validators";

import { authClient } from "~/server/auth/client";

const LoginForm = () => {
  const [showPassword, setShowPassword] = useState(false); // State to manage password visibility
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof AdminLoginSchema>>({
    resolver: zodResolver(AdminLoginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof AdminLoginSchema>) => {
    const { email, password } = values;
    setIsLoading(true);

    try {
      await authClient.signIn.email(
        {
          email: email,
          password: password,
        },
        {
          onSuccess: () => {
            window.location.href = "/";
            toast.success("Login successful!");
          },
          onError: () => {
            toast.error("Login failed. Please check your credentials.");
          },
        },
      );
    } catch (err) {
      console.error(err);
      toast.warning("Unauthorized");
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  return (
    <div className="flex flex-col gap-6">
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">Welcome back</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid gap-6">
                <div className="grid gap-6">
                  {/* email */}
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* password */}
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showPassword ? "text" : "password"}
                              placeholder="password@123"
                              {...field}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-1/2 -translate-y-1/2 transform"
                              onClick={togglePasswordVisibility}
                            >
                              {showPassword ? <EyeOff /> : <Eye />}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Link
                    href="/auth/forget-password"
                    className="text-sm text-teal-600 hover:text-teal-800"
                  >
                    Forgot password?
                  </Link>

                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Logging in..." : "Login"}
                  </Button>

                  <div className="text-center text-sm text-gray-600">
                    Don't have an account?{" "}
                    <Link
                      href="/auth/signup"
                      className="font-medium text-teal-600 hover:text-teal-800"
                    >
                      Sign up here
                    </Link>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default LoginForm;
