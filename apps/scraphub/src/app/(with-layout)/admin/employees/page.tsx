"use client";

import { redirect } from "next/navigation";

import { EmployeeManagement } from "~/components/admin/employee-management";
import { useSession } from "~/server/auth/client";

export default function EmployeesPage() {
  const { data: session, isPending } = useSession();

  if (isPending) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-teal-600"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // Check if user is authenticated and has admin role
  if (!session?.user) {
    redirect("/");
  }

  // For now, we'll allow access and let the tRPC middleware handle role checking
  // The scraphubAdminEmployeeProcedure will verify the user has admin role

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">
          Employee Management
        </h1>
        <p className="mt-2 text-gray-600">
          Manage your ScrapHub employees and their roles
        </p>
      </div>

      <EmployeeManagement />
    </div>
  );
}
