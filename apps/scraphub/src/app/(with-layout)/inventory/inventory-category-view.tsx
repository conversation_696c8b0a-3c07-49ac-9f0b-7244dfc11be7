"use client";

import Image from "next/image";

import { Badge } from "@acme/ui/components/ui/badge";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";

interface CategoryData {
  categoryId: string;
  categoryName: string;
  categoryImage: string;
  rateType: "PER_KG" | "PER_ITEM" | null;
  totalQuantity: string;
  totalValue: string;
  itemCount: number;
}

interface InventoryCategoryViewProps {
  data: CategoryData[];
  isLoading: boolean;
}

export function InventoryCategoryView({
  data,
  isLoading,
}: InventoryCategoryViewProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3, 4, 5, 6].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 w-3/4 rounded bg-gray-200"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 rounded bg-gray-200"></div>
                <div className="h-3 w-2/3 rounded bg-gray-200"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <div className="mb-2 text-gray-400">
            <svg
              className="h-12 w-12"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"
              />
            </svg>
          </div>
          <h3 className="mb-1 text-lg font-medium text-gray-900">
            No inventory items
          </h3>
          <p className="text-center text-gray-500">
            Your inventory is empty. Items will appear here when orders are
            marked as "WITH_SCRAPHUB".
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {data.map((category) => (
        <Card
          key={category.categoryId}
          className="transition-shadow hover:shadow-md"
        >
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-3">
              <div className="relative h-12 w-12 overflow-hidden rounded-lg bg-gray-100">
                <Image
                  src={category.categoryImage}
                  alt={category.categoryName}
                  fill
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/placeholder-category.png";
                  }}
                />
              </div>
              <div className="flex-1">
                <CardTitle className="text-lg">
                  {category.categoryName}
                </CardTitle>
                <Badge variant="secondary" className="text-xs">
                  {category.rateType === "PER_KG" ? "Per KG" : "Per Item"}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Quantity:</span>
                <span className="font-medium">
                  {parseFloat(category.totalQuantity).toLocaleString()}
                  {category.rateType === "PER_KG" ? " kg" : " items"}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Value:</span>
                <span className="font-bold text-green-600">
                  ₹{parseFloat(category.totalValue).toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Orders:</span>
                <span className="text-sm">
                  {category.itemCount} order
                  {category.itemCount !== 1 ? "s" : ""}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
