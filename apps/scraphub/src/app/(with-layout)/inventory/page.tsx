import { Suspense } from "react";

import { InventoryContent } from "./inventory-content";

export default function InventoryPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Inventory Management</h1>
        <p className="text-gray-600 mt-2">
          Track and manage all scrap items currently with your ScrapHub
        </p>
      </div>
      
      <Suspense fallback={<div>Loading inventory...</div>}>
        <InventoryContent />
      </Suspense>
    </div>
  );
}
