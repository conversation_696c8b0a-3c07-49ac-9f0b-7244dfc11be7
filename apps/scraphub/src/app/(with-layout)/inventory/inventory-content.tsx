"use client";

import { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Boxes, Package, RefreshCw, TrendingUp } from "lucide-react";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@acme/ui/components/ui/tabs";

import { useTRPC } from "~/trpc/react";
import { InventoryCategoryView } from "./inventory-category-view";
import { InventoryItemsList } from "./inventory-items-list";

export function InventoryContent() {
  const [activeTab, setActiveTab] = useState("overview");
  const trpc = useTRPC();

  const {
    data: inventorySummary,
    isLoading: summaryLoading,
    refetch: refetchSummary,
  } = useQuery(trpc.inventory.getInventorySummary.queryOptions());

  const {
    data: categoryData,
    isLoading: categoryLoading,
    refetch: refetchCategory,
  } = useQuery(trpc.inventory.getInventoryByCategory.queryOptions());

  const syncInventoryMutation = useMutation(
    trpc.inventory.syncInventory.mutationOptions({
      onSuccess: () => {
        refetchSummary();
        refetchCategory();
      },
    }),
  );

  const handleSyncInventory = () => {
    syncInventoryMutation.mutate();
  };

  if (summaryLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ₹
              {parseFloat(inventorySummary?.totalValue ?? "0").toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Current inventory value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {parseFloat(inventorySummary?.totalItems ?? "0").toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Items in inventory</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Boxes className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {categoryData?.length ?? 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Different categories
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Action Bar */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Inventory Details</h2>
          <p className="text-sm text-gray-600">
            Last updated:{" "}
            {inventorySummary?.lastUpdatedAt
              ? new Date(inventorySummary.lastUpdatedAt).toLocaleString()
              : "Never"}
          </p>
        </div>
        <Button
          onClick={handleSyncInventory}
          disabled={syncInventoryMutation.isPending}
          variant="outline"
        >
          <RefreshCw
            className={`mr-2 h-4 w-4 ${syncInventoryMutation.isPending ? "animate-spin" : ""}`}
          />
          Sync Inventory
        </Button>
      </div>

      {/* Tabs for different views */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Category Overview</TabsTrigger>
          <TabsTrigger value="items">All Items</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <InventoryCategoryView
            data={categoryData ?? []}
            isLoading={categoryLoading}
          />
        </TabsContent>

        <TabsContent value="items" className="space-y-4">
          <InventoryItemsList />
        </TabsContent>
      </Tabs>
    </div>
  );
}
