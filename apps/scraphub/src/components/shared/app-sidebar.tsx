"use client";

import * as React from "react";
import Image from "next/image";
import Link from "next/link";
import { redirect } from "next/navigation";

import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  <PERSON>barFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@acme/ui/components/ui/sidebar";

import { SIDEBAR_LINKS_1 } from "~/lib/constants";
import { useSession } from "~/server/auth/client";
import { NavUser } from "./nav-footer";
import { NavMain } from "./nav-main";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession();

  if (!session?.user) {
    redirect("/");
  }

  return (
    <Sidebar
      variant="sidebar"
      collapsible="offcanvas"
      {...props}
      className="border-r border-black-150"
    >
      <SidebarHeader className="border-b border-black-150 bg-white">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              asChild
              className="transition-colors duration-200 hover:bg-teal-50"
            >
              <Link href="/" className="group">
                <div className="relative aspect-video h-[50px]">
                  <Image
                    src="/static/logo/scraplo.svg"
                    alt="Scraplo Logo"
                    fill
                    className="transition-transform duration-200 group-hover:scale-105"
                  />
                </div>
                <div className="flex flex-col gap-0.5 leading-none">
                  <span className="text-lg font-bold text-teal-700">
                    Scraplo
                  </span>
                  <span className="text-xs font-medium text-black-500">
                    Scrap Hub
                  </span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className="bg-white">
        <NavMain items={SIDEBAR_LINKS_1.navMain} userRole={session.user.role} />
      </SidebarContent>
      <SidebarFooter className="border-t border-black-150 bg-black-50">
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
