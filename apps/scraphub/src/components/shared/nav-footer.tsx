"use client";

import { ChevronsUpDown, LogOut } from "lucide-react";

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@acme/ui/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@acme/ui/components/ui/sidebar";

import { authClient, useSession } from "~/server/auth/client";

export function NavUser() {
  const { isMobile } = useSidebar();
  const { data: session } = useSession();

  const confirmSignOut = async () => {
    await authClient.signOut();
  };

  const user = session?.user;

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="group mx-2 rounded-lg border border-transparent transition-all duration-200 hover:border-teal-200 hover:bg-teal-50 hover:shadow-sm data-[state=open]:bg-teal-50 data-[state=open]:text-teal-800"
            >
              <div className="relative">
                <Avatar className="h-9 w-9 rounded-lg ring-2 ring-black-150 transition-all duration-200 group-hover:ring-teal-300">
                  <AvatarImage
                    src={user?.image ?? undefined}
                    alt={user?.name}
                  />
                  <AvatarFallback className="rounded-lg bg-teal-600 font-bold text-white">
                    {user?.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-0.5 -right-0.5 size-3 rounded-full border-2 border-white bg-green-500 shadow-sm" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold text-black-800 transition-colors duration-200 group-hover:text-teal-800">
                  {user?.name}
                </span>
                <span className="truncate text-xs text-black-500 transition-colors duration-200 group-hover:text-teal-600">
                  {user?.email}
                </span>
              </div>
              <ChevronsUpDown className="ml-auto size-4 text-black-400 transition-colors duration-200 group-hover:text-teal-600" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg border border-black-150 bg-white shadow-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-3 rounded-t-lg bg-teal-50 px-3 py-3 text-left text-sm">
                <Avatar className="h-10 w-10 rounded-lg ring-2 ring-teal-200">
                  <AvatarImage
                    src={user?.image ?? undefined}
                    alt={user?.name}
                  />
                  <AvatarFallback className="rounded-lg bg-teal-600 font-bold text-white">
                    {user?.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold text-black-800">
                    {user?.name}
                  </span>
                  <span className="truncate text-xs text-black-500">
                    {user?.email}
                  </span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="mx-1 gap-2 rounded-md text-red-600 transition-colors duration-200 hover:bg-red-50 hover:text-red-700"
              onClick={confirmSignOut}
            >
              <LogOut className="size-4" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
