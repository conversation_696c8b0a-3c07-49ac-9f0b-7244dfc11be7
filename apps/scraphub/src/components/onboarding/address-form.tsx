import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import ScraphubMap from "@acme/ui/components/google/scraphub-map";
import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import GoogleAutocompleteInput from "@acme/ui/components/ui/google-autocomplete-input";
import { Input } from "@acme/ui/components/ui/input";

import type { AddressForm } from "./types";
import { addressSchema } from "./types";

// Default coordinates (Delhi, India)
const DEFAULT_COORDINATES = { latitude: 28.6139, longitude: 77.209 };

interface AddressFormProps {
  onSubmit: (data: AddressForm) => Promise<void>;
  onError?: () => void;
  onBack?: () => void;
  isSubmitting: boolean;
  initialData?: Partial<AddressForm>;
}

export function AddressForm({
  onSubmit,
  onError,
  onBack,
  isSubmitting,
  initialData,
}: AddressFormProps) {
  const [autocompleteInput, setAutocompleteInput] = useState<string | null>(
    null,
  );

  const form = useForm<AddressForm>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      display: "",
      street: "",
      city: "",
      state: "",
      country: "",
      postalCode: "",
      coordinates: DEFAULT_COORDINATES,
      // localAddress: "",
      landmark: "",
    },
  });

  // Prefill form with initial data when available
  useEffect(() => {
    if (initialData) {
      form.reset({
        display: initialData.display ?? "",
        street: initialData.street ?? "",
        city: initialData.city ?? "",
        state: initialData.state ?? "",
        country: initialData.country ?? "",
        postalCode: initialData.postalCode ?? "",
        coordinates: initialData.coordinates ?? DEFAULT_COORDINATES,
        // localAddress: initialData.localAddress ?? "",
        landmark: initialData.landmark ?? "",
      });

      // Set autocomplete input if display address is available
      if (initialData.display) {
        setAutocompleteInput(initialData.display);
      }
    }
  }, [initialData, form]);

  const selectedLocationCoordinates = form.watch("coordinates");

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit, onError)}
        className="space-y-4"
      >
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Scraphub Address Details</h3>

          <GoogleAutocompleteInput
            showSearchIcon
            showAutoDetectLocationIcon
            placeholder="Search for location"
            initialValue={autocompleteInput ?? ""}
            showClearButton
            onInputChange={(value) => {
              setAutocompleteInput(value);
            }}
            onLocationSelect={(location) => {
              if (location.address?.display) {
                form.setValue("display", location.address.display);
              }
              if (location.address?.street) {
                form.setValue("street", location.address.street);
              }
              if (location.address?.city) {
                form.setValue("city", location.address.city);
              }
              if (location.address?.state) {
                form.setValue("state", location.address.state);
              }
              if (location.address?.country) {
                form.setValue("country", location.address.country);
              }
              if (location.address?.postalCode) {
                form.setValue("postalCode", location.address.postalCode);
              }
              form.setValue("coordinates", {
                latitude: location.latitude,
                longitude: location.longitude,
              });
            }}
            onUserLocationDetect={(location) => {
              if (location.address?.display) {
                form.setValue("display", location.address.display);
              }
              if (location.address?.street) {
                form.setValue("street", location.address.street);
              }
              if (location.address?.city) {
                form.setValue("city", location.address.city);
              }
              if (location.address?.state) {
                form.setValue("state", location.address.state);
              }
              if (location.address?.country) {
                form.setValue("country", location.address.country);
              }
              if (location.address?.postalCode) {
                form.setValue("postalCode", location.address.postalCode);
              }
              form.setValue("coordinates", {
                latitude: location.latitude,
                longitude: location.longitude,
              });
            }}
          />

          <ScraphubMap
            selectedLocationCoordinates={selectedLocationCoordinates}
          />

          <FormField
            control={form.control}
            name="street"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Street/ Block number *</FormLabel>
                <FormControl>
                  <Input placeholder="eg: Block Ab" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="landmark"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Landmark/ Road/ Area/ Locality *</FormLabel>
                <FormControl>
                  <Input placeholder="eg: Nearby xyz." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-center gap-4 pt-4">
          {onBack && (
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              disabled={isSubmitting}
            >
              Back
            </Button>
          )}
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-teal-600 px-8 hover:bg-teal-700"
          >
            {isSubmitting ? "Saving..." : "Save & Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
