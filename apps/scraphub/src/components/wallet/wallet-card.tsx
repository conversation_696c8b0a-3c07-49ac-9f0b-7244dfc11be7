"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  HandCoins,
  Plus,
  Wallet,
  XCircle,
} from "lucide-react";

import { Alert, AlertDescription } from "@acme/ui/components/ui/alert";
import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";

import { useTRPC } from "~/trpc/react";
import { DepositModal } from "../deposit/deposit-modal";
import { PayoutModal } from "../payout/payout-modal";

export function WalletCard() {
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [showPayoutModal, setShowPayoutModal] = useState(false);
  const trpc = useTRPC();

  const {
    data: walletInfo,
    isLoading,
    error,
  } = useQuery(trpc.payment.getWalletInfo.queryOptions());

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            Wallet
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="mb-4 h-8 rounded bg-gray-200"></div>
            <div className="mb-2 h-4 rounded bg-gray-200"></div>
            <div className="h-10 rounded bg-gray-200"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!walletInfo) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            Wallet
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load wallet information
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Show loading or error based on walletInfo API response
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            Wallet
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load wallet information
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            Wallet
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="mb-4 h-8 rounded bg-gray-200"></div>
            <div className="mb-2 h-4 rounded bg-gray-200"></div>
            <div className="h-10 rounded bg-gray-200"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getApprovalStatusBadge = () => {
    if (!walletInfo) {
      return null;
    }
    switch (walletInfo.adminApprovalStatus) {
      case "APPROVED":
        return (
          <Badge
            variant="default"
            className="border-green-200 bg-green-100 text-green-800"
          >
            <CheckCircle className="mr-1 h-3 w-3" />
            Approved
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge variant="destructive">
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </Badge>
        );
      case "PENDING":
      default:
        return (
          <Badge
            variant="secondary"
            className="border-amber-200 bg-amber-100 text-amber-800"
          >
            <Clock className="mr-1 h-3 w-3" />
            Pending Approval
          </Badge>
        );
    }
  };

  const getApprovalMessage = () => {
    if (!walletInfo) {
      return {
        type: "error" as const,
        message: "Failed to load wallet information",
      };
    }
    switch (walletInfo.adminApprovalStatus) {
      case "APPROVED":
        return {
          type: "success" as const,
          message: `Account approved on ${walletInfo.adminApprovedAt ? new Date(walletInfo.adminApprovedAt).toLocaleDateString() : "N/A"}. You can now make deposits.`,
        };
      case "REJECTED":
        return {
          type: "error" as const,
          message:
            walletInfo.adminRejectionReason ||
            "Your account has been rejected. Please contact support.",
        };
      case "PENDING":
      default:
        return {
          type: "warning" as const,
          message:
            "Your account is pending admin approval. You cannot make deposits until approved.",
        };
    }
  };

  const approvalMessage = getApprovalMessage();

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Wallet className="h-5 w-5" />
              Wallet Balance
            </div>
            {getApprovalStatusBadge()}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="text-3xl font-bold text-green-600">
              ₹{walletInfo.walletBalance.toLocaleString()}
            </div>
            <p className="text-sm text-muted-foreground">Available balance</p>
          </div>

          <Alert
            className={
              approvalMessage.type === "success"
                ? "border-green-200 bg-green-50"
                : approvalMessage.type === "error"
                  ? "border-red-200 bg-red-50"
                  : "border-amber-200 bg-amber-50"
            }
          >
            <AlertCircle
              className={`h-4 w-4 ${
                approvalMessage.type === "success"
                  ? "text-green-600"
                  : approvalMessage.type === "error"
                    ? "text-red-600"
                    : "text-amber-600"
              }`}
            />
            <AlertDescription
              className={
                approvalMessage.type === "success"
                  ? "text-green-800"
                  : approvalMessage.type === "error"
                    ? "text-red-800"
                    : "text-amber-800"
              }
            >
              {approvalMessage.message}
            </AlertDescription>
          </Alert>

          <div className="flex gap-2">
            <Button
              onClick={() => setShowDepositModal(true)}
              disabled={!walletInfo.canDeposit}
              className="flex-1"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Money
            </Button>
            <Button
              onClick={() => setShowPayoutModal(true)}
              disabled={!walletInfo.canDeposit || walletInfo.walletBalance <= 0}
              variant="outline"
              className="flex-1"
            >
              <HandCoins className="mr-2 h-4 w-4" />
              Withdraw
            </Button>
          </div>

          {!walletInfo.canDeposit && (
            <div className="text-center">
              <Button variant="outline" disabled className="w-full">
                Financial Operations Disabled
              </Button>
            </div>
          )}

          {walletInfo.canDeposit && (
            <p className="text-xs text-muted-foreground">
              Minimum deposit: ₹
              {walletInfo.minimumDepositAmount.toLocaleString()}
            </p>
          )}
        </CardContent>
      </Card>

      <DepositModal
        open={showDepositModal}
        onOpenChange={setShowDepositModal}
        walletInfo={walletInfo}
      />

      <PayoutModal
        open={showPayoutModal}
        onOpenChange={setShowPayoutModal}
        walletInfo={{
          balance: walletInfo.walletBalance,
          canPayout: walletInfo.canDeposit, // Same approval requirement
        }}
      />
    </>
  );
}
