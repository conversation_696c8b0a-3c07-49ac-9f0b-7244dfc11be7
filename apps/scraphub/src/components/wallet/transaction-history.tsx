"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  ArrowDownCircle,
  ArrowUpCircle,
  CheckCircle,
  Clock,
  History,
  XCircle,
} from "lucide-react";

import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";

import { useTRPC } from "~/trpc/react";

export function TransactionHistory() {
  const [limit] = useState(20);
  const [offset] = useState(0);
  const trpc = useTRPC();

  const { data: transactionData, isLoading } = useQuery(
    trpc.payment.getTransactionHistory.queryOptions({ limit, offset }),
  );

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "SUCCESS":
        return (
          <Badge
            variant="default"
            className="border-green-200 bg-green-100 text-green-800"
          >
            <CheckCircle className="mr-1 h-3 w-3" />
            Success
          </Badge>
        );
      case "FAILED":
        return (
          <Badge variant="destructive">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        );
      case "PENDING":
      default:
        return (
          <Badge
            variant="secondary"
            className="border-amber-200 bg-amber-100 text-amber-800"
          >
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
    }
  };

  const getTransactionIcon = (type: string, transactionFor: string) => {
    if (type === "CREDIT") {
      return <ArrowUpCircle className="h-4 w-4 text-green-600" />;
    } else {
      return <ArrowDownCircle className="h-4 w-4 text-red-600" />;
    }
  };

  const formatTransactionFor = (transactionFor: string) => {
    switch (transactionFor) {
      case "DEPOSIT":
        return "Wallet Deposit";
      case "WITHDRAWAL":
        return "Withdrawal";
      case "ORDER_PAYMENT":
        return "Order Payment";
      case "REFUND":
        return "Refund";
      default:
        return transactionFor;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Transaction History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="flex items-center gap-3">
                    <div className="h-8 w-8 rounded-full bg-gray-200"></div>
                    <div>
                      <div className="mb-2 h-4 w-32 rounded bg-gray-200"></div>
                      <div className="h-3 w-24 rounded bg-gray-200"></div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="mb-2 h-4 w-20 rounded bg-gray-200"></div>
                    <div className="h-3 w-16 rounded bg-gray-200"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const transactions = transactionData?.transactions ?? [];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Transaction History
        </CardTitle>
      </CardHeader>
      <CardContent>
        {transactions.length === 0 ? (
          <div className="py-8 text-center">
            <History className="mx-auto mb-4 h-12 w-12 text-gray-400" />
            <p className="text-gray-500">No transactions yet</p>
            <p className="text-sm text-gray-400">
              Your transaction history will appear here
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between rounded-lg border p-4 transition-colors hover:bg-gray-50"
              >
                <div className="flex items-center gap-3">
                  {getTransactionIcon(
                    transaction.transactionType,
                    transaction.transactionFor,
                  )}
                  <div>
                    <div className="font-medium">
                      {formatTransactionFor(transaction.transactionFor)}
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(transaction.createdAt).toLocaleDateString()} •{" "}
                      {transaction.paymentGateway}
                    </div>
                    {transaction.description && (
                      <div className="mt-1 text-xs text-gray-400">
                        {transaction.description}
                      </div>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div
                    className={`font-medium ${
                      transaction.transactionType === "CREDIT"
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {transaction.transactionType === "CREDIT" ? "+" : "-"}₹
                    {transaction.amount.toLocaleString()}
                  </div>
                  <div className="mt-1">
                    {getStatusBadge(transaction.status)}
                  </div>
                </div>
              </div>
            ))}

            {transactions.length >= limit && (
              <div className="pt-4 text-center">
                <Button variant="outline" size="sm">
                  Load More
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
