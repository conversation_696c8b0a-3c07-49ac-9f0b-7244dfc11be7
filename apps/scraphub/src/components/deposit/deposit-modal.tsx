"use client";

import { useState } from "react";
// @ts-expect-error
import { load as loadCashfree } from "@cashfreepayments/cashfree-js";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AlertCircle, CreditCard, Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { Alert, AlertDescription } from "@acme/ui/components/ui/alert";
import { Button } from "@acme/ui/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@acme/ui/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { tryCatch } from "@acme/validators/utils";

import { env } from "~/env";
import { useTRPC } from "~/trpc/react";

const DepositSchema = z.object({
  amount: z.number().min(200000, "Minimum deposit amount is ₹2,00,000"),
});

type DepositFormData = z.infer<typeof DepositSchema>;

interface DepositModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  walletInfo: {
    canDeposit: boolean;
    adminApprovalStatus: string;
    minimumDepositAmount: number;
  };
}

export function DepositModal({
  open,
  onOpenChange,
  walletInfo,
}: DepositModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const queryClient = useQueryClient();
  const trpc = useTRPC();

  const form = useForm<DepositFormData>({
    resolver: zodResolver(DepositSchema),
    defaultValues: {
      amount: walletInfo.minimumDepositAmount,
    },
  });

  const { mutate: createDepositOrder, isPending: isCreatingOrder } =
    useMutation(
      trpc.payment.createDepositOrder.mutationOptions({
        onSuccess: async (orderData) => {
          console.log("orderData", orderData);
          setIsProcessing(true);
          try {
            await handlePayment(orderData);
          } catch (error) {
            console.error("Payment failed:", error);
            setIsProcessing(false);
          }
        },
        onError: (error) => {
          console.error("Failed to create deposit order:", error);
        },
      }),
    );

  const { mutate: verifyDeposit } = useMutation(
    trpc.payment.verifyDeposit.mutationOptions({
      onSuccess: async () => {
        form.reset();
      },
      onError: () => {
        toast.error(
          "Unable to verify payment we are working on it. If amount is deducted from your account, it will be updated in your wallet shortly.",
        );
      },
      onSettled: async () => {
        setIsProcessing(false);
        onOpenChange(false);
        await queryClient.invalidateQueries({
          queryKey: trpc.payment.getWalletInfo.queryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: trpc.payment.getTransactionHistory.queryKey(),
        });
      },
    }),
  );

  const handlePayment = async (orderData: {
    orderId: string;
    transactionId: string;
    name: string;
    amount: number;
    currency: string;
    description: string;
    gateway: string;
    payment_session_id: string | undefined;
  }) => {
    // Handle Cashfree payment using @cashfreepayments/cashfree-js
    console.log("Cashfree payment", orderData);
    try {
      const { data: cashfree, err } = await tryCatch(
        loadCashfree({
          mode: env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT,
        }),
      );

      if (err) {
        toast.error(
          "Unable to initiate payment we are working on it. Please try again later.",
        );
        setIsProcessing(false);
        return;
      }

      const checkoutOptions = {
        paymentSessionId: orderData.payment_session_id,
        redirectTarget: "_modal",
      };
      onOpenChange(false);

      const result = await cashfree.checkout(checkoutOptions);

      console.log("Cashfree payment result", result);

      if (result.error) {
        console.error("Cashfree payment failed:", result.error);
        setIsProcessing(false);
      } else {
        // Payment completed, verify with backend
        verifyDeposit({
          orderId: orderData.orderId,
          // paymentId: result.paymentDetails?.paymentId,
          // paymentGateway: "CASHFREE",
        });
      }
    } catch (error) {
      console.error("Cashfree payment error:", error);
      setIsProcessing(false);
    }
  };

  const onSubmit = (data: DepositFormData) => {
    if (!walletInfo.canDeposit) {
      return;
    }
    createDepositOrder(data);
  };

  if (!walletInfo.canDeposit) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Account Approval Required
            </DialogTitle>
            <DialogDescription>
              Your account needs to be approved by an admin before you can make
              deposits.
            </DialogDescription>
          </DialogHeader>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {walletInfo.adminApprovalStatus === "PENDING" &&
                "Your account is currently pending approval. Please wait for admin approval."}
              {walletInfo.adminApprovalStatus === "REJECTED" &&
                "Your account has been rejected. Please contact support for more information."}
            </AlertDescription>
          </Alert>

          <div className="flex justify-end">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Money to Wallet</DialogTitle>
          <DialogDescription>
            Minimum deposit amount is ₹
            {walletInfo.minimumDepositAmount.toLocaleString()}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount (₹)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="200000"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                      disabled={isCreatingOrder || isProcessing}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter the amount you want to deposit (minimum ₹
                    {walletInfo.minimumDepositAmount.toLocaleString()})
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isCreatingOrder || isProcessing}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isCreatingOrder || isProcessing}
                className="flex-1"
              >
                {isCreatingOrder || isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isCreatingOrder
                      ? "Creating Order..."
                      : "Processing Payment..."}
                  </>
                ) : (
                  <>
                    <CreditCard className="mr-2 h-4 w-4" />
                    Proceed to Pay
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
