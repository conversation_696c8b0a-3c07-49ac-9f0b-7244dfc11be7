"use client";

import { useState } from "react";
import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { EmployeeDataTable } from "./employee-data-table";
import { EmployeeFormSheet } from "./employee-form-sheet";

export function EmployeeManagement() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingEmployeeId, setEditingEmployeeId] = useState<string | null>(
    null,
  );

  const handleEditEmployee = (employeeId: string) => {
    setEditingEmployeeId(employeeId);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingEmployeeId(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Employees</h2>
          <p className="text-sm text-gray-600">
            Manage your organization's employees and their access levels
          </p>
        </div>
        <Button onClick={() => setIsFormOpen(true)}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Employee
        </Button>
      </div>

      <EmployeeDataTable onEditEmployee={handleEditEmployee} />

      <EmployeeFormSheet
        isOpen={isFormOpen}
        onClose={handleCloseForm}
        editingEmployeeId={editingEmployeeId}
      />
    </div>
  );
}
