"use client";

import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@acme/ui/components/ui/sheet";

import { useSession } from "~/server/auth/client";
import { useTRPC } from "~/trpc/react";

const EmployeeSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  email: z.string().email({ message: "Valid email is required" }),
  role: z.enum(["ADMIN", "EMPLOYEE"]).default("ADMIN"),
});

type EmployeeFormValues = z.infer<typeof EmployeeSchema>;

interface EmployeeFormSheetProps {
  isOpen: boolean;
  onClose: () => void;
  editingEmployeeId: string | null;
}

export function EmployeeFormSheet({
  isOpen,
  onClose,
  editingEmployeeId,
}: EmployeeFormSheetProps) {
  const { data: session } = useSession();
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<EmployeeFormValues>({
    resolver: zodResolver(EmployeeSchema),
    defaultValues: {
      name: "",
      email: "",
      role: "ADMIN",
    },
  });

  const isUpdateMode = !!editingEmployeeId;

  // Load employee data when editing
  const { data: employee, isLoading: isEmployeeLoading } = useQuery(
    trpc.admin.employee.getEmployeeById.queryOptions(
      { employeeId: editingEmployeeId! },
      { enabled: isUpdateMode && !!editingEmployeeId },
    ),
  );

  const createEmployeeMutation = useMutation(
    trpc.admin.employee.createEmployee.mutationOptions({
      onSuccess: () => {
        toast.success("Employee created successfully");
        form.reset();
        onClose();
        queryClient.invalidateQueries({
          queryKey: trpc.admin.employee.getEmployees.queryKey(),
        });
      },
      onError: (error: any) => {
        toast.error(error.message);
      },
    }),
  );

  const updateEmployeeMutation = useMutation(
    trpc.admin.employee.updateEmployee.mutationOptions({
      onSuccess: () => {
        toast.success("Employee updated successfully");
        form.reset();
        onClose();
        queryClient.invalidateQueries({
          queryKey: trpc.admin.employee.getEmployees.queryKey(),
        });
      },
      onError: (error: any) => {
        toast.error(error.message);
      },
    }),
  );

  // Reset form when employee data is loaded or mode changes
  useEffect(() => {
    if (isUpdateMode) {
      if (employee) {
        form.reset({
          name: employee.name,
          email: employee.email,
          role: employee.role,
        });
      }
    } else {
      form.reset({
        name: "",
        email: "",
        role: "ADMIN",
      });
    }
  }, [employee, form, isUpdateMode]);

  const onSubmit = async (data: EmployeeFormValues) => {
    setIsLoading(true);

    try {
      if (isUpdateMode && editingEmployeeId) {
        await updateEmployeeMutation.mutateAsync({
          ...data,
          employeeId: editingEmployeeId,
        });
      } else {
        // The scraphubId will be obtained from the session context in the tRPC procedure
        await createEmployeeMutation.mutateAsync(data);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isEmployeeLoading) {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent className="sm:max-w-[500px]">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading employee data...</span>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="sm:max-w-[500px]">
        <SheetHeader>
          <SheetTitle>
            {isUpdateMode ? "Edit Employee" : "Add New Employee"}
          </SheetTitle>
          <SheetDescription>
            {isUpdateMode
              ? "Update employee information. Changes will be saved immediately."
              : "Create a new employee account. System will generate credentials and send them via email."}
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter employee name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter email address"
                        {...field}
                      />
                    </FormControl>
                    {!isUpdateMode && (
                      <FormDescription>
                        Login credentials will be sent to this email.
                      </FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select employee role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ADMIN">Admin</SelectItem>
                        <SelectItem value="EMPLOYEE">Employee</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Admin users have full access, while employees have limited
                      access.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-2 pt-4">
                <Button type="submit" disabled={isLoading} className="flex-1">
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isUpdateMode ? "Updating..." : "Creating..."}
                    </>
                  ) : isUpdateMode ? (
                    "Update Employee"
                  ) : (
                    "Create Employee"
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </SheetContent>
    </Sheet>
  );
}
