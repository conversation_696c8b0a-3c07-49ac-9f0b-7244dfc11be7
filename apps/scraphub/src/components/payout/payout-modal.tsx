"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AlertCircle, HandCoins, Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Alert, AlertDescription } from "@acme/ui/components/ui/alert";
import { Button } from "@acme/ui/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@acme/ui/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { Textarea } from "@acme/ui/components/ui/textarea";

import { useTRPC } from "~/trpc/react";

const PayoutSchema = z.object({
  fund_account_id: z.string().min(1, "Please select a fund account"),
  amount: z.number().positive("Amount must be positive"),
  mode: z.enum(["IMPS", "NEFT", "RTGS", "UPI"]),
  purpose: z.string().min(1, "Purpose is required"),
  narration: z.string().optional(),
});

type PayoutFormData = z.infer<typeof PayoutSchema>;

interface PayoutModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  walletInfo: {
    balance: number;
    canPayout: boolean;
  };
}

export function PayoutModal({
  open,
  onOpenChange,
  walletInfo,
}: PayoutModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const queryClient = useQueryClient();
  const trpc = useTRPC();

  const form = useForm<PayoutFormData>({
    resolver: zodResolver(PayoutSchema),
    defaultValues: {
      amount: 0,
      mode: "IMPS",
      purpose: "",
      narration: "",
    },
  });

  // Get gateway info
  const { data: gatewayInfo } = useQuery(
    trpc.payout.getGatewayInfo.queryOptions(),
  );

  const { mutate: createPayout, isPending: isCreatingPayout } = useMutation(
    trpc.payout.createPayout.mutationOptions({
      onSuccess: async (data) => {
        await queryClient.invalidateQueries({
          queryKey: ["payment", "getWalletInfo"],
        });
        await queryClient.invalidateQueries({
          queryKey: ["payment", "getTransactionHistory"],
        });
        setIsProcessing(false);
        onOpenChange(false);
        form.reset();
      },
      onError: (error) => {
        console.error("Payout failed:", error);
        setIsProcessing(false);
      },
    }),
  );

  const onSubmit = (data: PayoutFormData) => {
    if (!walletInfo.canPayout) {
      return;
    }

    if (data.amount > walletInfo.balance) {
      form.setError("amount", { message: "Insufficient wallet balance" });
      return;
    }

    setIsProcessing(true);
    createPayout(data);
  };

  if (!walletInfo.canPayout) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Payout Not Available
            </DialogTitle>
            <DialogDescription>
              Your account needs to be approved before you can make payouts.
            </DialogDescription>
          </DialogHeader>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Please ensure your account is approved by admin before attempting
              payouts.
            </AlertDescription>
          </Alert>

          <div className="flex justify-end">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Withdraw</DialogTitle>
          <DialogDescription>
            Withdraw money from your wallet to a registered fund account
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount (₹)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="10000"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                      disabled={isCreatingPayout || isProcessing}
                    />
                  </FormControl>
                  <FormDescription>
                    Available balance: ₹{walletInfo.balance.toLocaleString()}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="fund_account_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Fund Account</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select fund account" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="demo_account_1">
                        Demo Bank Account - ****1234
                      </SelectItem>
                      <SelectItem value="demo_account_2">
                        Demo UPI - demo@upi
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Select the destination account for the payout
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="mode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Transfer Mode</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="IMPS">IMPS (Instant)</SelectItem>
                      <SelectItem value="NEFT">NEFT (2-4 hours)</SelectItem>
                      <SelectItem value="RTGS">RTGS (Real-time)</SelectItem>
                      <SelectItem value="UPI">UPI (Instant)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="purpose"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Purpose</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Vendor payment, Salary"
                      {...field}
                      disabled={isCreatingPayout || isProcessing}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="narration"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Narration (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional details..."
                      {...field}
                      disabled={isCreatingPayout || isProcessing}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* {gatewayInfo && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Payout will be processed via {gatewayInfo.primary} gateway
                </AlertDescription>
              </Alert>
            )} */}

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isCreatingPayout || isProcessing}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isCreatingPayout || isProcessing}
                className="flex-1"
              >
                {isCreatingPayout || isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <HandCoins className="mr-2 h-4 w-4" />
                    Withdraw
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
