import { createAnalytics } from "@acme/analytics";

import { env } from "~/env";

// Initialize ScrapHub analytics
export const analytics = createAnalytics({
  apiKey: env.NEXT_PUBLIC_POSTHOG_KEY ?? "",
  apiHost: env.NEXT_PUBLIC_POSTHOG_HOST,
  appPrefix: "scraphub",
  debug: env.NODE_ENV === "development",
  disabled: !env.NEXT_PUBLIC_POSTHOG_KEY,
});

// Initialize analytics on client side
if (typeof window !== "undefined") {
  analytics.initialize();
}

export default analytics;
