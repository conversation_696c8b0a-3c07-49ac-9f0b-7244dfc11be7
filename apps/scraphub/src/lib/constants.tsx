import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  User,
  Users,
} from "lucide-react";

export const SIDEBAR_LINKS_1 = {
  navMain: [
    {
      title: "Dashboard",
      url: "/",
      icon: LayoutDashboard,
      isActive: true,
      items: [],
    },
    {
      title: "Profile",
      url: "/profile",
      icon: User,
      isActive: true,
      items: [],
    },
    {
      title: "Inventory",
      url: "/inventory",
      icon: Package,
      isActive: true,
      items: [],
    },
    {
      title: "Orders",
      url: "#",
      icon: ShoppingCart,
      isActive: true,
      items: [
        {
          title: "All Orders",
          url: "/orders/all-orders",
        },
        {
          title: "Pending Orders",
          url: "/orders/pending-orders",
        },
        {
          title: "Completed Orders",
          url: "/orders/completed-orders",
        },
      ],
    },
    {
      title: "Admin",
      url: "#",
      icon: Users,
      isActive: false,
      items: [
        {
          title: "Employee Management",
          url: "/admin/employees",
        },
      ],
    },
  ],
};
