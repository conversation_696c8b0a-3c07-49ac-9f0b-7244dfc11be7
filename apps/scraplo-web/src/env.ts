import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  shared: {
    NODE_ENV: z
      .enum(["development", "production", "test"])
      .default("development"),
  },
  /**
   * Specify your server-side environment variables schema here.
   * This way you can ensure the app isn't built with invalid env vars.
   */
  server: {
    POSTGRES_URL: z.string().url(),
    SELLER_BETTER_AUTH_SECRET: z.string().nonempty(),
    // Cashfree configuration
    CASHFREE_APP_ID: z.string().nonempty(),
    CASHFREE_SECRET_KEY: z.string().nonempty(),
    GETSTREAM_APP_ID: z.string().optional(),
    GETSTREAM_API_SECRET: z.string().nonempty(),
    GETSTREAM_API_KEY: z.string().nonempty(),
    ONESIGNAL_CUSTOMER_KEY: z.string().nonempty(),
    UPLOADTHING_TOKEN: z.string().nonempty(),
  },

  /**
   * Specify your client-side environment variables schema here.
   * For them to be exposed to the client, prefix them with `NEXT_PUBLIC_`.
   */
  client: {
    NEXT_PUBLIC_SELLER_BETTER_AUTH_URL: z.string().nonempty(),
    NEXT_PUBLIC_GOOGLE_MAPS_KEY: z.string().nonempty(),
    NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_ID: z.string().nonempty(),
    NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_SAFARI_WEB_ID: z.string().nonempty(),
    NEXT_PUBLIC_POSTHOG_KEY: z.string().nonempty(),
    NEXT_PUBLIC_POSTHOG_HOST: z.string().nonempty(),
    NEXT_PUBLIC_GETSTREAM_API_KEY: z.string().nonempty(),
    NEXT_PUBLIC_CASHFREE_ENVIRONMENT: z
      .enum(["sandbox", "production"])
      .default("sandbox"),
  },
  /**
   * Destructure all variables from `process.env` to make sure they aren't tree-shaken away.
   */
  experimental__runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_SELLER_BETTER_AUTH_URL:
      process.env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL,
    NEXT_PUBLIC_GOOGLE_MAPS_KEY: process.env.NEXT_PUBLIC_GOOGLE_MAPS_KEY,
    NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_ID:
      process.env.NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_ID,
    NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_SAFARI_WEB_ID:
      process.env.NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_SAFARI_WEB_ID,
    NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,
    NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,
    NEXT_PUBLIC_GETSTREAM_API_KEY: process.env.NEXT_PUBLIC_GETSTREAM_API_KEY,
    NEXT_PUBLIC_CASHFREE_ENVIRONMENT:
      process.env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT,
    // NEXT_PUBLIC_CLIENTVAR: process.env.NEXT_PUBLIC_CLIENTVAR,
  },
  skipValidation:
    !!process.env.CI || process.env.npm_lifecycle_event === "lint",
});
