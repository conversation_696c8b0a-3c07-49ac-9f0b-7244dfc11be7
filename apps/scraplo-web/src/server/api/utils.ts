import { StreamClient } from "@stream-io/node-sdk";

import { Cashfree } from "@acme/cashfree-sdk";
import { getOneSignalClient } from "@acme/onesignal";
import { OneSignalUserType } from "@acme/onesignal/src/types";

import { env } from "~/env";

export const cashfree = new Cashfree({
  appId: env.CASHFREE_APP_ID,
  secretKey: env.CASHFREE_SECRET_KEY,
  environment:
    (env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT as "sandbox" | "production") ||
    "sandbox",
});

export const streamClient = new StreamClient(
  env.GETSTREAM_API_KEY,
  env.GETSTREAM_API_SECRET,
  {
    timeout: 10000, // 10 seconds timeout for all requests
  },
);

export const onesignalKabadiwalaClient = getOneSignalClient(
  OneSignalUserType.KABADIWALA,
);

export const onesignalSellerClient = getOneSignalClient(
  OneSignalUserType.SELLER,
);
export const onesignalAdminClient = getOneSignalClient(OneSignalUserType.ADMIN);
