import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { and, eq } from "@acme/db";
import { payout, seller } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";
import { 
  createSimpleTransferRequest,
  createTransferId,
  validateTransferRequest 
} from "@acme/cashfree-sdk";

import { createTRPCRouter, protectedProcedure } from "../trpc";
import { cashfree } from "../utils";

export const sellerPayoutRouter = createTRPCRouter({
  // Get seller payout history
  getPayoutHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      const sellerId = ctx.session.user.id;

      const { data: payouts, err } = await tryCatch(
        ctx.db.query.payout.findMany({
          where: and(
            eq(payout.userId, sellerId),
            eq(payout.userType, "SELLER")
          ),
          limit: input.limit,
          offset: input.offset,
          orderBy: (payouts, { desc }) => [desc(payouts.createdAt)],
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch payout history",
        });
      }

      return payouts || [];
    }),

  // Get specific payout status
  getPayoutStatus: protectedProcedure
    .input(z.object({ transferId: z.string() }))
    .query(async ({ ctx, input }) => {
      const sellerId = ctx.session.user.id;

      // Verify payout belongs to seller
      const { data: payoutRecord, err: payoutErr } = await tryCatch(
        ctx.db.query.payout.findFirst({
          where: and(
            eq(payout.transferId, input.transferId),
            eq(payout.userId, sellerId),
            eq(payout.userType, "SELLER")
          ),
        }),
      );

      if (payoutErr || !payoutRecord) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Payout not found",
        });
      }

      // Get latest status from Cashfree
      const { data, error } = await cashfree.getTransferStatus({
        transfer_id: input.transferId,
      });

      if (error) {
        // Return database status if API call fails
        return {
          transferId: payoutRecord.transferId,
          status: payoutRecord.status,
          amount: Number(payoutRecord.amount),
          updatedAt: payoutRecord.updatedAt,
        };
      }

      // Update database with latest status if changed
      if (data && data.status !== payoutRecord.status) {
        await tryCatch(
          ctx.db
            .update(payout)
            .set({
              status: data.status,
              transferUtr: data.transfer_utr,
              transferServiceCharge: data.transfer_service_charge ? String(data.transfer_service_charge) : null,
              transferServiceTax: data.transfer_service_tax ? String(data.transfer_service_tax) : null,
              updatedAt: new Date(),
              processedAt: data.status === "SUCCESS" ? new Date() : null,
            })
            .where(eq(payout.transferId, input.transferId)),
        );
      }

      return {
        transferId: data?.transfer_id,
        cfTransferId: data?.cf_transfer_id,
        status: data?.status,
        amount: data?.transfer_amount,
        utr: data?.transfer_utr,
        serviceCharge: data?.transfer_service_charge,
        serviceTax: data?.transfer_service_tax,
        addedOn: data?.added_on,
        updatedOn: data?.updated_on,
      };
    }),

  // Internal method to create payout (called by kabadiwala app)
  createSellerPayout: protectedProcedure
    .input(
      z.object({
        sellerId: z.string(),
        amount: z.number().min(1),
        orderId: z.string().optional(),
        remarks: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get seller details
      const { data: sellerData, err: sellerErr } = await tryCatch(
        ctx.db.query.seller.findFirst({
          where: eq(seller.id, input.sellerId),
          columns: {
            cashfreeBeneficiaryId: true,
            isPaymentVerified: true,
            fullName: true,
          },
        }),
      );

      if (sellerErr || !sellerData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Seller not found",
        });
      }

      if (!sellerData.cashfreeBeneficiaryId) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Seller payment details not set up",
        });
      }

      if (!sellerData.isPaymentVerified) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Seller payment details not verified",
        });
      }

      // Generate unique transfer ID
      const transferId = createTransferId("seller_payout", `${input.sellerId}_${Date.now()}`);

      try {
        const transferRequest = createSimpleTransferRequest({
          transferId,
          amount: input.amount,
          beneficiaryId: sellerData.cashfreeBeneficiaryId,
          transferMode: "imps",
          remarks: input.remarks || `Payout for seller ${sellerData.fullName}`,
        });

        // Validate request
        const validationErrors = validateTransferRequest(transferRequest);
        if (validationErrors.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Validation failed: ${validationErrors.join(", ")}`,
          });
        }

        const { data, error } = await cashfree.createTransfer(transferRequest);

        if (error) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to initiate payout",
          });
        }

        // Store payout record in database
        const { err: insertErr } = await tryCatch(
          ctx.db.insert(payout).values({
            transferId,
            cfTransferId: data?.cf_transfer_id,
            userType: "SELLER",
            userId: input.sellerId,
            beneficiaryId: sellerData.cashfreeBeneficiaryId,
            amount: String(input.amount),
            currency: "INR",
            payoutType: input.orderId ? "ORDER_PAYOUT" : "MANUAL_PAYOUT",
            transferMode: "imps",
            status: data?.status || "RECEIVED",
            remarks: input.remarks,
            purpose: input.orderId ? `Order payout for order ${input.orderId}` : "Manual payout",
            gatewayMetadata: {
              orderId: input.orderId,
              initiatedBy: "KABADIWALA",
            },
            createdAt: new Date(),
            updatedAt: new Date(),
          }),
        );

        if (insertErr) {
          console.error("Failed to record payout:", insertErr);
        }

        return {
          transferId: data?.transfer_id,
          status: data?.status,
          amount: data?.transfer_amount,
        };
      } catch (error) {
        console.error("Seller payout error:", error);
        throw error;
      }
    }),

  // Get seller payout summary
  getPayoutSummary: protectedProcedure.query(async ({ ctx }) => {
    const sellerId = ctx.session.user.id;

    const { data: payouts, err } = await tryCatch(
      ctx.db.query.payout.findMany({
        where: and(
          eq(payout.userId, sellerId),
          eq(payout.userType, "SELLER")
        ),
      }),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch payout summary",
      });
    }

    const summary = (payouts || []).reduce(
      (acc, p) => {
        const amount = Number(p.amount);
        acc.totalAmount += amount;
        
        if (p.status === "SUCCESS") {
          acc.successfulAmount += amount;
          acc.successfulCount += 1;
        } else if (p.status === "PENDING" || p.status === "RECEIVED") {
          acc.pendingAmount += amount;
          acc.pendingCount += 1;
        } else if (p.status === "FAILED" || p.status === "REJECTED") {
          acc.failedAmount += amount;
          acc.failedCount += 1;
        }
        
        return acc;
      },
      {
        totalAmount: 0,
        successfulAmount: 0,
        pendingAmount: 0,
        failedAmount: 0,
        successfulCount: 0,
        pendingCount: 0,
        failedCount: 0,
      },
    );

    return summary;
  }),
});
