import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { Cashfree } from "@acme/cashfree-sdk";
import { eq } from "@acme/db";
import { customerPaymentTransaction } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../../env";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Initialize Cashfree client for payouts
const cashfree = new Cashfree({
  appId: env.CASHFREE_APP_ID,
  secretKey: env.CASHFREE_SECRET_KEY,
  environment:
    (env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT as "sandbox" | "production") ||
    "sandbox",
});

// Schemas
const CreateContactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email().optional(),
  contact: z.string().optional(),
  type: z.enum(["customer", "vendor", "employee"]).default("customer"),
  reference_id: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

const CreateFundAccountSchema = z
  .object({
    contact_id: z.string(),
    account_type: z.enum(["bank_account", "vpa"]),
    bank_account: z
      .object({
        name: z.string(),
        account_number: z.string(),
        ifsc: z.string(),
      })
      .optional(),
    vpa: z
      .object({
        address: z.string(),
      })
      .optional(),
  })
  .refine(
    (data) => {
      if (data.account_type === "bank_account" && !data.bank_account) {
        return false;
      }
      if (data.account_type === "vpa" && !data.vpa) {
        return false;
      }
      return true;
    },
    {
      message: "Account details must match the account type",
    },
  );

const CreatePayoutSchema = z.object({
  fund_account_id: z.string(),
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().default("INR"),
  mode: z.enum(["IMPS", "NEFT", "RTGS", "UPI"]),
  purpose: z.string(),
  reference_id: z.string().optional(),
  narration: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

export const payoutRouter = createTRPCRouter({
  // Get payout gateway info
  getGatewayInfo: protectedProcedure.query(async () => {
    return {
      primary: "CASHFREE",
      fallback: null,
      environment: env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT || "sandbox",
    };
  }),

  // Contact management (Cashfree uses beneficiaries instead of contacts)
  createContact: protectedProcedure
    .input(CreateContactSchema)
    .mutation(async ({ input }) => {
      // For Cashfree, we create a beneficiary instead of a contact
      const beneficiaryData = {
        bene_id: `contact_${Date.now()}`,
        name: input.name,
        email: input.email,
        phone: input.contact,
      };

      const { data, error } = await cashfree.createBeneficiary(beneficiaryData);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to create beneficiary",
        });
      }

      return {
        id: data?.bene_id,
        name: input.name,
        email: input.email,
        contact: input.contact,
      };
    }),

  // Fund account management (Cashfree uses beneficiaries with payment details)
  createFundAccount: protectedProcedure
    .input(CreateFundAccountSchema)
    .mutation(async ({ input }) => {
      // For Cashfree, we create a beneficiary with payment details
      const beneficiaryData: any = {
        bene_id: `fund_${Date.now()}`,
        name: "Fund Account", // This would need to be passed from input or fetched from contact
      };

      // Add payment method details based on account type
      if (input.account_type === "bank_account" && input.bank_account) {
        beneficiaryData.bank_account = input.bank_account.account_number;
        beneficiaryData.ifsc = input.bank_account.ifsc;
      } else if (input.account_type === "vpa" && input.vpa) {
        beneficiaryData.vpa = input.vpa.address;
      }

      const { data, error } = await cashfree.createBeneficiary(beneficiaryData);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to create fund account",
        });
      }

      return {
        id: data?.bene_id,
        contact_id: input.contact_id,
        account_type: input.account_type,
        active: true,
      };
    }),

  getAllFundAccounts: protectedProcedure
    .input(z.object({ contact_id: z.string() }))
    .query(async ({ input }) => {
      // For Cashfree, we would list beneficiaries
      // For now, return empty array as we don't have a direct mapping
      return [];
    }),

  deactivateFundAccount: protectedProcedure
    .input(z.object({ fund_account_id: z.string() }))
    .mutation(async ({ input }) => {
      // For Cashfree, we would remove/deactivate a beneficiary
      // For now, return success as we don't have direct mapping
      return { success: true };
    }),

  // Payout operations (limited for customers - mainly for refunds)
  createRefundPayout: protectedProcedure
    .input(
      CreatePayoutSchema.extend({
        order_id: z.string().optional(),
        refund_reason: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // This would typically be called by admin for customer refunds
      // For now, we'll allow customers to request refunds to their registered accounts

      const { data: customerData, err: customerErr } = await tryCatch(
        ctx.db.query.customer.findFirst({
          where: eq(customer.id, ctx.session.user.id),
          columns: {
            walletBalance: true,
          },
        }),
      );

      if (customerErr || !customerData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Customer not found",
        });
      }

      // Create Cashfree payout for refund
      const payoutData = {
        transfer_id: `refund_${Date.now()}`,
        bene_id: input.fund_account_id,
        amount: input.amount,
        remarks: `Refund: ${input.refund_reason}`,
      };

      const { data, error } = await cashfree.createPayout(payoutData);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to create refund payout",
        });
      }

      // Record refund transaction
      const { err: insertErr } = await tryCatch(
        ctx.db.insert(customerPaymentTransaction).values({
          customerId: ctx.session.user.id,
          amount: input.amount.toString(),
          paymentGateway: "CASHFREE",
          status: "PENDING",
          currency: input.currency,
          transactionFor: "REFUND",
          transactionType: "CREDIT",
          gatewayMetadata: {
            payout_id: data?.transfer_id,
            fund_account_id: input.fund_account_id,
            refund_reason: input.refund_reason,
            order_id: input.order_id,
          },
        }),
      );

      if (insertErr) {
        console.error("Failed to record refund transaction:", insertErr);
      }

      return data;
    }),

  getPayoutStatus: protectedProcedure
    .input(z.object({ payout_id: z.string() }))
    .query(async ({ input }) => {
      // Get payout status from Cashfree
      const { data, error } = await cashfree.getPayoutStatus(input.payout_id);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to get payout status",
        });
      }

      return data;
    }),

  // Validation (Cashfree doesn't have direct fund account validation)
  validateFundAccount: protectedProcedure
    .input(
      z.object({
        fund_account_id: z.string(),
        amount: z.number().positive(),
        currency: z.string().default("INR"),
      }),
    )
    .mutation(async ({ input }) => {
      // For Cashfree, we can't validate fund accounts directly
      // Return success for now
      return {
        success: true,
        message: "Fund account validation not available with Cashfree",
      };
    }),

  // Get customer wallet balance
  getWalletBalance: protectedProcedure.query(async ({ ctx }) => {
    const { data: customerData, err } = await tryCatch(
      ctx.db.query.customer.findFirst({
        where: eq(customer.id, ctx.session.user.id),
        columns: {
          walletBalance: true,
        },
      }),
    );

    if (err || !customerData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Customer not found",
      });
    }

    return {
      balance: Number(customerData.walletBalance),
      currency: "INR",
    };
  }),

  // Get refund history
  getRefundHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: transactions, err } = await tryCatch(
        ctx.db.query.customerPaymentTransaction.findMany({
          where: eq(customerPaymentTransaction.customerId, ctx.session.user.id),
          orderBy: (transactions, { desc }) => [desc(transactions.createdAt)],
          limit: input.limit,
          offset: input.offset,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch refund history",
        });
      }

      // Filter only refund transactions
      const refunds =
        transactions?.filter((t) => t.transactionFor === "REFUND") || [];

      return {
        refunds: refunds.map((t) => ({
          id: t.id,
          amount: Number(t.amount),
          currency: t.currency,
          status: t.status,
          refund_reason: t.gatewayMetadata?.refund_reason || "N/A",
          order_id: t.gatewayMetadata?.order_id || null,
          createdAt: t.createdAt,
          updatedAt: t.updatedAt,
        })),
      };
    }),
});
