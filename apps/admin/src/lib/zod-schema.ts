import { z } from "zod";

import { categoryRateTypeEnum } from "@acme/db/schema";
import { phoneNumberSchema } from "@acme/validators";

// Blog validation schemas
export const BlogStatusEnum = z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]);

export const CreateBlogSchema = z.object({
  title: z
    .string()
    .min(1, "Title is required")
    .max(255, "Title must be less than 255 characters"),
  slug: z
    .string()
    .min(1, "Slug is required")
    .max(255, "Slug must be less than 255 characters")
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      "Slug must be lowercase letters, numbers, and hyphens only",
    ),
  excerpt: z.string().optional(),
  content: z.string().min(1, "Content is required"),
  banner: z.string().url("Banner must be a valid URL"),
  status: BlogStatusEnum.default("DRAFT"),
  isActive: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  publishedAt: z.date().optional(),
});

export const UpdateBlogSchema = CreateBlogSchema.partial();

export const BlogFilterSchema = z.object({
  status: BlogStatusEnum.optional(),
  isActive: z.boolean().optional(),
  isFeatured: z.boolean().optional(),
  tag: z.string().optional(),
});

export const BlogIdSchema = z.object({
  id: z.string(),
});

export const BlogSlugSchema = z.object({
  slug: z.string(),
});

export type CreateBlogInput = z.infer<typeof CreateBlogSchema>;
export type UpdateBlogInput = z.infer<typeof UpdateBlogSchema>;
export type BlogFilterInput = z.infer<typeof BlogFilterSchema>;

export const CreateTestimonialSchema = z.object({
  title: z
    .string()
    .nonempty("Title is required")
    .max(100, "Title must be less than 100 characters"),
  message: z.string().nonempty("Message is required"),
  stars: z.number().min(1).max(5).default(5),
  userImage: z.string().url().optional(),
  userName: z.string().max(100).optional(),
  userDesignation: z.string().max(100).optional(),
  userLocation: z.string().max(100).optional(),
});

const AddressSchema = z.object({
  display: z.string().nonempty({ message: "Display name is required." }),
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  coordinates: z.object({
    latitude: z.number(),
    longitude: z.number(),
  }),
  localAddress: z.string().nonempty({ message: "Local address is required." }),
  landmark: z.string().nonempty({ message: "Landmark is required." }),
});

export const ScraphubSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(255, "Name must be less than 255 characters"),
  phoneNumber: phoneNumberSchema,
  address: AddressSchema,
});

export const ScraphubEmployee = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  email: z.string().email({ message: "Valid email is required" }),
  role: z.enum(["ADMIN", "EMPLOYEE"]).default("ADMIN"),
});

export const MarkRegionSchema = z.object({
  name: z.string().min(1, {
    message: "Region name is required.",
  }),
  isActive: z.boolean().default(true),
  markersLatLng: z
    .array(
      z.object({
        latitude: z.number(),
        longitude: z.number(),
      }),
    )
    .min(3, {
      message: "Please add at least 3 points to create a polygon on the map.",
    }),
});

export const CreateRegionPricingPeriodSchema = z.object({
  regionId: z.string().min(1, "Region ID is required"),
  effectiveFromDate: z.string().date("Invalid date format"),
  expiresAt: z.string().date("Invalid date format"),
});

export const CreateRegionPricingPeriodWithCategoryPricingSchema =
  CreateRegionPricingPeriodSchema.extend({
    regionCategoryPricing: z.array(
      z.object({
        id: z.string().min(1, "Category ID is required"),
        name: z.string().min(1, "Category name is required"),
        rate: z.number().min(1, "Rate must be a positive number"),
        rateType: z.enum(categoryRateTypeEnum.enumValues),
        compensationKabadiwalaRate: z
          .number()
          .min(0, "Compensation Kabadiwala rate must be a positive number"),
        compensationRecyclerRate: z
          .number()
          .min(0, "Compensation Recycler rate must be a positive number"),
      }),
    ),
  });

export const UpdateRegionPricingPeriodSchema = z.object({
  id: z.string().min(1, "Pricing period ID is required"),
  effectiveFromDate: z.string().date("Invalid date format").optional(),
  expiresAt: z.string().date("Invalid date format").optional(),
});

export const DeleteRegionPricingPeriodSchema = z.object({
  id: z.string().min(1, "Pricing period ID is required"),
});

export const SetCategoryPricingForRegionSchema = z.object({
  pricingHistoryId: z.string().min(1, "Pricing history ID is required"),
  categoryId: z.string().min(1, "Category ID is required"),
  rate: z.string().min(1, "Rate is required"),
  compensationKabadiwalaRate: z.string().optional(),
  compensationRecyclerRate: z.string().optional(),
});

export const UpdateCategoryPricingSchema = z.object({
  id: z.string().min(1, "Pricing ID is required"),
  rate: z.string().optional(),
  compensationKabadiwalaRate: z.string().optional(),
  compensationRecyclerRate: z.string().optional(),
});

export const DeleteCategoryPricingSchema = z.object({
  id: z.string().min(1, "Pricing ID is required"),
});
