import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { and, desc, eq, inArray, like, or } from "@acme/db";
import { payout } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { createTRPCRouter, protectedProcedure } from "../trpc";

export const payoutRouter = createTRPCRouter({
  // Get all payouts with filtering and pagination
  getAllPayouts: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
        userType: z.enum(["SELLER", "KABADIWALA", "SCRAPHUB"]).optional(),
        status: z.enum([
          "RECEIVED",
          "APPROVAL_PENDING", 
          "PENDING",
          "SUCCESS",
          "FAILED",
          "REJECTED",
          "REVERSED",
        ]).optional(),
        payoutType: z.enum([
          "WALLET_WITHDRAWAL",
          "ORDER_PAYOUT",
          "REFUND",
          "MANUAL_PAYOUT",
        ]).optional(),
        search: z.string().optional(),
        dateFrom: z.date().optional(),
        dateTo: z.date().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const conditions = [];

      // Add filters
      if (input.userType) {
        conditions.push(eq(payout.userType, input.userType));
      }
      if (input.status) {
        conditions.push(eq(payout.status, input.status));
      }
      if (input.payoutType) {
        conditions.push(eq(payout.payoutType, input.payoutType));
      }
      if (input.search) {
        conditions.push(
          or(
            like(payout.transferId, `%${input.search}%`),
            like(payout.userId, `%${input.search}%`),
            like(payout.remarks, `%${input.search}%`),
          ),
        );
      }
      if (input.dateFrom) {
        conditions.push(eq(payout.createdAt, input.dateFrom));
      }
      if (input.dateTo) {
        conditions.push(eq(payout.createdAt, input.dateTo));
      }

      const { data: payouts, err } = await tryCatch(
        ctx.db.query.payout.findMany({
          where: conditions.length > 0 ? and(...conditions) : undefined,
          limit: input.limit,
          offset: input.offset,
          orderBy: [desc(payout.createdAt)],
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch payouts",
        });
      }

      // Get total count for pagination
      const { data: totalCount, err: countErr } = await tryCatch(
        ctx.db.query.payout.findMany({
          where: conditions.length > 0 ? and(...conditions) : undefined,
        }),
      );

      return {
        payouts: payouts || [],
        totalCount: totalCount?.length || 0,
        hasMore: (input.offset + input.limit) < (totalCount?.length || 0),
      };
    }),

  // Get payout statistics
  getPayoutStats: protectedProcedure
    .input(
      z.object({
        userType: z.enum(["SELLER", "KABADIWALA", "SCRAPHUB"]).optional(),
        dateFrom: z.date().optional(),
        dateTo: z.date().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const conditions = [];

      if (input.userType) {
        conditions.push(eq(payout.userType, input.userType));
      }
      if (input.dateFrom) {
        conditions.push(eq(payout.createdAt, input.dateFrom));
      }
      if (input.dateTo) {
        conditions.push(eq(payout.createdAt, input.dateTo));
      }

      const { data: payouts, err } = await tryCatch(
        ctx.db.query.payout.findMany({
          where: conditions.length > 0 ? and(...conditions) : undefined,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch payout statistics",
        });
      }

      const stats = (payouts || []).reduce(
        (acc, p) => {
          const amount = Number(p.amount);
          acc.totalAmount += amount;
          acc.totalCount += 1;

          // By status
          if (p.status === "SUCCESS") {
            acc.successfulAmount += amount;
            acc.successfulCount += 1;
          } else if (p.status === "PENDING" || p.status === "RECEIVED") {
            acc.pendingAmount += amount;
            acc.pendingCount += 1;
          } else if (p.status === "FAILED" || p.status === "REJECTED") {
            acc.failedAmount += amount;
            acc.failedCount += 1;
          }

          // By user type
          if (p.userType === "SELLER") {
            acc.sellerAmount += amount;
            acc.sellerCount += 1;
          } else if (p.userType === "KABADIWALA") {
            acc.kabadiwalaAmount += amount;
            acc.kabadiwalaCount += 1;
          } else if (p.userType === "SCRAPHUB") {
            acc.scraphubAmount += amount;
            acc.scraphubCount += 1;
          }

          // By payout type
          if (p.payoutType === "WALLET_WITHDRAWAL") {
            acc.withdrawalAmount += amount;
            acc.withdrawalCount += 1;
          } else if (p.payoutType === "ORDER_PAYOUT") {
            acc.orderPayoutAmount += amount;
            acc.orderPayoutCount += 1;
          }

          return acc;
        },
        {
          totalAmount: 0,
          totalCount: 0,
          successfulAmount: 0,
          successfulCount: 0,
          pendingAmount: 0,
          pendingCount: 0,
          failedAmount: 0,
          failedCount: 0,
          sellerAmount: 0,
          sellerCount: 0,
          kabadiwalaAmount: 0,
          kabadiwalaCount: 0,
          scraphubAmount: 0,
          scraphubCount: 0,
          withdrawalAmount: 0,
          withdrawalCount: 0,
          orderPayoutAmount: 0,
          orderPayoutCount: 0,
        },
      );

      return stats;
    }),

  // Get specific payout details
  getPayoutDetails: protectedProcedure
    .input(z.object({ transferId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: payoutData, err } = await tryCatch(
        ctx.db.query.payout.findFirst({
          where: eq(payout.transferId, input.transferId),
        }),
      );

      if (err || !payoutData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Payout not found",
        });
      }

      return payoutData;
    }),

  // Update payout status (for manual intervention)
  updatePayoutStatus: protectedProcedure
    .input(
      z.object({
        transferId: z.string(),
        status: z.enum([
          "RECEIVED",
          "APPROVAL_PENDING", 
          "PENDING",
          "SUCCESS",
          "FAILED",
          "REJECTED",
          "REVERSED",
        ]),
        remarks: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(payout)
          .set({
            status: input.status,
            statusDescription: input.remarks,
            updatedAt: new Date(),
            processedAt: input.status === "SUCCESS" ? new Date() : null,
          })
          .where(eq(payout.transferId, input.transferId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update payout status",
        });
      }

      return { success: true };
    }),

  // Get failed payouts that need attention
  getFailedPayouts: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: payouts, err } = await tryCatch(
        ctx.db.query.payout.findMany({
          where: inArray(payout.status, ["FAILED", "REJECTED", "REVERSED"]),
          limit: input.limit,
          offset: input.offset,
          orderBy: [desc(payout.createdAt)],
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch failed payouts",
        });
      }

      return payouts || [];
    }),

  // Get pending payouts that need approval
  getPendingPayouts: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: payouts, err } = await tryCatch(
        ctx.db.query.payout.findMany({
          where: inArray(payout.status, ["RECEIVED", "APPROVAL_PENDING", "PENDING"]),
          limit: input.limit,
          offset: input.offset,
          orderBy: [desc(payout.createdAt)],
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch pending payouts",
        });
      }

      return payouts || [];
    }),

  // Bulk update payout statuses
  bulkUpdatePayouts: protectedProcedure
    .input(
      z.object({
        transferIds: z.array(z.string()),
        status: z.enum([
          "RECEIVED",
          "APPROVAL_PENDING", 
          "PENDING",
          "SUCCESS",
          "FAILED",
          "REJECTED",
          "REVERSED",
        ]),
        remarks: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(payout)
          .set({
            status: input.status,
            statusDescription: input.remarks,
            updatedAt: new Date(),
            processedAt: input.status === "SUCCESS" ? new Date() : null,
          })
          .where(inArray(payout.transferId, input.transferIds)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to bulk update payouts",
        });
      }

      return { 
        success: true, 
        updatedCount: input.transferIds.length 
      };
    }),
});
