import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import bcrypt from "bcryptjs";
import { and, eq, ilike, isNull, or } from "drizzle-orm";
import { z } from "zod";

import {
  kabadiwala,
  scraphub,
  scraphubAddress,
  scraphubEmployee,
  scraphubEmployeeAccount,
} from "@acme/db/schema";
import { sendEmail } from "@acme/mail";
import { tryCatch } from "@acme/validators/utils";

import { ScraphubEmployee, ScraphubSchema } from "~/lib/zod-schema";
import { protectedProcedure } from "../trpc";

export const scraphubRouter = {
  getAllScraphubs: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          id: scraphub.id,
          name: scraphub.name,
          phoneNumber: scraphub.phoneNumber,
          adminApprovalStatus: scraphub.adminApprovalStatus,
          adminApprovedAt: scraphub.adminApprovedAt,
          adminApprovedBy: scraphub.adminApprovedBy,
          adminRejectionReason: scraphub.adminRejectionReason,
          walletBalance: scraphub.walletBalance,
          createdAt: scraphub.createdAt,
          address: {
            id: scraphubAddress.id,
            display: scraphubAddress.display,
            street: scraphubAddress.street,
            city: scraphubAddress.city,
            state: scraphubAddress.state,
            country: scraphubAddress.country,
            postalCode: scraphubAddress.postalCode,
            coordinates: scraphubAddress.coordinates,
            landmark: scraphubAddress.landmark,
            createdAt: scraphubAddress.createdAt,
          },
        })
        .from(scraphub)
        .leftJoin(scraphubAddress, eq(scraphub.id, scraphubAddress.scraphubId)),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch scraphubs",
      });
    }

    return data;
  }),

  getScraphubById: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: scraphub.id,
            name: scraphub.name,
            address: {
              id: scraphubAddress.id,
              display: scraphubAddress.display,
              street: scraphubAddress.street,
              city: scraphubAddress.city,
              state: scraphubAddress.state,
              country: scraphubAddress.country,
              postalCode: scraphubAddress.postalCode,
              coordinates: scraphubAddress.coordinates,
              landmark: scraphubAddress.landmark,
            },
            phoneNumber: scraphub.phoneNumber,
          })
          .from(scraphub)
          .leftJoin(
            scraphubAddress,
            eq(scraphub.id, scraphubAddress.scraphubId),
          )
          .where(eq(scraphub.id, input.scraphubId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch scraphub",
        });
      }

      if (!data[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Scraphub not found",
        });
      }

      return data[0];
    }),

  createScraphub: protectedProcedure
    .input(ScraphubSchema)
    .mutation(async ({ input, ctx }) => {
      const { err: txErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          const [scraphubData] = await tx
            .insert(scraphub)
            .values({
              name: input.name,
              phoneNumber: input.phoneNumber,
            })
            .returning({ id: scraphub.id });

          if (!scraphubData) {
            throw new Error("Failed to create scraphub");
          }

          await tx.insert(scraphubAddress).values({
            display: input.address.display,
            street: input.address.street,
            city: input.address.city,
            state: input.address.state,
            country: input.address.country,
            postalCode: input.address.postalCode,
            coordinates: input.address.coordinates,
            localAddress: input.address.localAddress,
            landmark: input.address.landmark,
            scraphubId: scraphubData.id,
          });
        }),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to create scraphub",
        });
      }

      return {
        message: "Scraphub created successfully",
      };
    }),

  updateScraphub: protectedProcedure
    .input(
      ScraphubSchema.extend({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err: txErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          await tx
            .update(scraphub)
            .set({
              name: input.name,
              phoneNumber: input.phoneNumber,
            })
            .where(eq(scraphub.id, input.scraphubId));

          await tx
            .update(scraphubAddress)
            .set({
              display: input.address.display,
              street: input.address.street,
              city: input.address.city,
              state: input.address.state,
              country: input.address.country,
              postalCode: input.address.postalCode,
              coordinates: input.address.coordinates,
              localAddress: input.address.localAddress,
              landmark: input.address.landmark,
              updatedAt: new Date(),
            })
            .where(eq(scraphubAddress.scraphubId, input.scraphubId));
        }),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to update scraphub",
        });
      }

      return {
        message: "Scraphub updated successfully",
      };
    }),

  deleteScraphub: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db.delete(scraphub).where(eq(scraphub.id, input.scraphubId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete scraphub",
        });
      }

      return {
        message: "Scraphub deleted successfully",
      };
    }),

  createEmployee: protectedProcedure
    .input(
      ScraphubEmployee.extend({
        scraphubId: z.string().nonempty({ message: "Scraphub IDis required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Check if email already exists
      const { data: existingEmployee, err: existingErr } = await tryCatch(
        ctx.db
          .select({ id: scraphubEmployee.id })
          .from(scraphubEmployee)
          .where(eq(scraphubEmployee.email, input.email)),
      );

      if (existingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing employee",
        });
      }

      if (existingEmployee[0]) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already registered",
        });
      }

      // Generate a random password
      const generatedPassword = Math.random().toString(36).slice(-8);

      // Hash the password
      const hashedPassword = await bcrypt.hash(generatedPassword, 10);

      const { err: txErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          const [scraphubEmployeeRes] = await tx
            .insert(scraphubEmployee)
            .values({
              scraphubId: input.scraphubId,
              name: input.name,
              email: input.email,
              role: input.role,
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .returning();

          if (!scraphubEmployeeRes) {
            throw new Error("Failed to create scraphub employee");
          }

          await tx.insert(scraphubEmployeeAccount).values({
            scraphubEmployeeId: scraphubEmployeeRes.id,
            accountId: scraphubEmployeeRes.id,
            password: hashedPassword,
            providerId: "credential",
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to create employee",
        });
      }

      await sendEmail({
        to: input.email,
        subject: "Welcome to Scraplo",
        content: `Hello ${input.name},\n\nYour account has been created successfully. Here are your credentials:\n\nEmail: ${input.email}\nPassword: ${generatedPassword}\n\nPlease log in to the portal and change your password.\n\nThank you!`,
      });

      return {
        message: "Employee created successfully. Credentials sent to email.",
      };
    }),

  getAllEmployees: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: employees, err: employeesErr } = await tryCatch(
        ctx.db.query.scraphubEmployee.findMany({
          where: eq(scraphubEmployee.scraphubId, input.scraphubId),
        }),
      );

      if (employeesErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch employees",
        });
      }

      return employees;
    }),

  getEmployeeById: protectedProcedure
    .input(
      z.object({
        employeeId: z.string().nonempty({ message: "Employee ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: employee, err: employeeErr } = await tryCatch(
        ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, input.employeeId),
        }),
      );

      if (employeeErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch employee",
        });
      }

      if (!employee) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employee not found",
        });
      }

      return employee;
    }),

  updateEmployee: protectedProcedure
    .input(
      ScraphubEmployee.extend({
        employeeId: z.string().nonempty({ message: "Employee ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Check if email already exists for other employees
      const { data: existingEmployee, err: existingErr } = await tryCatch(
        ctx.db
          .select({ id: scraphubEmployee.id })
          .from(scraphubEmployee)
          .where(eq(scraphubEmployee.email, input.email)),
      );

      if (existingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing employee",
        });
      }

      if (existingEmployee[0] && existingEmployee[0].id !== input.employeeId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already registered",
        });
      }

      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(scraphubEmployee)
          .set({
            name: input.name,
            email: input.email,
            role: input.role,
            updatedAt: new Date(),
          })
          .where(eq(scraphubEmployee.id, input.employeeId)),
      );

      if (updateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: updateErr.message || "Failed to update employee",
        });
      }

      return {
        message: "Employee updated successfully",
      };
    }),

  searchKabadiwalas: protectedProcedure
    .input(
      z.object({
        query: z.string().min(1, { message: "Search query is required" }),
        scraphubId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.query.kabadiwala.findMany({
          columns: {
            id: true,
            name: true,
            image: true,
            phoneNumber: true,
            phoneNumberVerified: true,
            averageRating: true,
            scraphubId: true,
            createdAt: true,
          },
          where: and(
            or(isNull(kabadiwala.isBlocked), eq(kabadiwala.isBlocked, false)),
            or(
              eq(kabadiwala.scraphubId, input.scraphubId),
              isNull(kabadiwala.scraphubId),
            ),
            ilike(kabadiwala.name, `%${input.query}%`),
          ),
          limit: 10,
        }),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Failed to search kabadiwalas",
        });
      }
      return { data };
    }),

  getAllAssociatedKabadiwalas: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: kabadiwalas, err: kabadiwalasErr } = await tryCatch(
        ctx.db.query.kabadiwala.findMany({
          where: eq(kabadiwala.scraphubId, input.scraphubId),
          columns: {
            id: true,
            name: true,
            phoneNumber: true,
            averageRating: true,
            image: true,
            isOnDuty: true,
            isBlocked: true,
          },
        }),
      );

      if (kabadiwalasErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwalas",
        });
      }

      return kabadiwalas;
    }),

  assignKabadiwala: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
        kabadiwalaId: z
          .string()
          .nonempty({ message: "Kabadiwala ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            scraphubId: input.scraphubId,
          })
          .where(eq(kabadiwala.id, input.kabadiwalaId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to assign kabadiwala",
        });
      }

      return {
        message: "Kabadiwala assigned successfully",
      };
    }),

  removeAssignedKabadiwala: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
        kabadiwalaId: z
          .string()
          .nonempty({ message: "Kabadiwala ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            scraphubId: null,
          })
          .where(eq(kabadiwala.id, input.kabadiwalaId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to remove kabadiwala assignment",
        });
      }

      return {
        message: "Kabadiwala assignment removed successfully",
      };
    }),

  // Approval Management
  approveScraphub: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
        adminNotes: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(scraphub)
          .set({
            adminApprovalStatus: "APPROVED",
            adminApprovedAt: new Date(),
            adminApprovedBy: ctx.session.user.id,
            adminRejectionReason: null, // Clear any previous rejection reason
          })
          .where(eq(scraphub.id, input.scraphubId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to approve scraphub",
        });
      }

      // Get scraphub details for notification
      const { data: scraphubData } = await tryCatch(
        ctx.db.query.scraphub.findFirst({
          where: eq(scraphub.id, input.scraphubId),
          columns: {
            name: true,
            phoneNumber: true,
          },
        }),
      );

      // Send approval notification email (if email is available)
      // if (scraphubData) {
      //   try {
      //     await sendEmail({
      //       to: `${scraphubData}@scraplo.com`, // Placeholder email
      //       subject: "ScrapHub Account Approved",
      //       html: `
      //         <h2>Congratulations! Your ScrapHub account has been approved.</h2>
      //         <p>Dear ${scraphubData.name},</p>
      //         <p>Your ScrapHub account has been approved by our admin team. You can now:</p>
      //         <ul>
      //           <li>Make deposits to your wallet (minimum ₹2,00,000)</li>
      //           <li>Create and manage payouts</li>
      //           <li>Access all ScrapHub features</li>
      //         </ul>
      //         <p>Thank you for choosing Scraplo!</p>
      //         ${input.adminNotes ? `<p><strong>Admin Notes:</strong> ${input.adminNotes}</p>` : ""}
      //       `,
      //     });
      //   } catch (emailErr) {
      //     console.error("Failed to send approval email:", emailErr);
      //   }
      // }

      return {
        message: "Scraphub approved successfully",
      };
    }),

  rejectScraphub: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
        rejectionReason: z
          .string()
          .min(1, { message: "Rejection reason is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(scraphub)
          .set({
            adminApprovalStatus: "REJECTED",
            adminApprovedAt: null,
            adminApprovedBy: null,
            adminRejectionReason: input.rejectionReason,
          })
          .where(eq(scraphub.id, input.scraphubId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to reject scraphub",
        });
      }

      // Get scraphub details for notification
      const { data: scraphubData } = await tryCatch(
        ctx.db.query.scraphub.findFirst({
          where: eq(scraphub.id, input.scraphubId),
          columns: {
            name: true,
            phoneNumber: true,
          },
        }),
      );

      // Send rejection notification email
      // if (scraphubData) {
      //   try {
      //     await sendEmail({
      //       to: `${scraphubData.phoneNumber}@scraplo.com`, // Placeholder email
      //       subject: "ScrapHub Account Application Update",
      //       html: `
      //         <h2>ScrapHub Account Application Update</h2>
      //         <p>Dear ${scraphubData.name},</p>
      //         <p>We regret to inform you that your ScrapHub account application has been rejected.</p>
      //         <p><strong>Reason:</strong> ${input.rejectionReason}</p>
      //         <p>If you believe this is an error or would like to reapply, please contact our support team.</p>
      //         <p>Thank you for your interest in Scraplo.</p>
      //       `,
      //     });
      //   } catch (emailErr) {
      //     console.error("Failed to send rejection email:", emailErr);
      //   }
      // }

      return {
        message: "Scraphub rejected successfully",
      };
    }),

  resetApprovalStatus: protectedProcedure
    .input(
      z.object({
        scraphubId: z.string().nonempty({ message: "Scraphub ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(scraphub)
          .set({
            adminApprovalStatus: "PENDING",
            adminApprovedAt: null,
            adminApprovedBy: null,
            adminRejectionReason: null,
          })
          .where(eq(scraphub.id, input.scraphubId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to reset approval status",
        });
      }

      return {
        message: "Approval status reset to pending",
      };
    }),

  getApprovalStats: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          status: scraphub.adminApprovalStatus,
        })
        .from(scraphub),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch approval stats",
      });
    }

    const stats = data?.reduce(
      (acc, item) => {
        const status = item.status || "PENDING";
        acc[status] = (acc[status] || 0) + 1;
        acc.total += 1;
        return acc;
      },
      { PENDING: 0, APPROVED: 0, REJECTED: 0, total: 0 },
    ) || { PENDING: 0, APPROVED: 0, REJECTED: 0, total: 0 };

    return stats;
  }),
} satisfies TRPCRouterRecord;
