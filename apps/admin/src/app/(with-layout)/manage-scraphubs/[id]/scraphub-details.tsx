"use client";

import Link from "next/link";
import { useSuspenseQuery } from "@tanstack/react-query";
import { Building2, ExternalLink, Globe, MapPin, Phone } from "lucide-react";

import { CopyToClipboardButton } from "@acme/ui/components/ui/copy-to-clipboard";

import { useTRPC } from "~/trpc/react";
import ScraphubEmployeesDataTable from "./scraphub-employees-data-table";
import ScraphubKabadiwalasDataTable from "./scraphub-kabadiwalas-data-table";

interface ScraphubDetailsProps {
  scraphubId: string;
}

const ScraphubDetails = ({ scraphubId }: ScraphubDetailsProps) => {
  const trpc = useTRPC();
  const { data, isError } = useSuspenseQuery(
    trpc.scraphub.getScraphubById.queryOptions({
      scraphubId: scraphubId,
    }),
  );
  const { data: employees } = useSuspenseQuery(
    trpc.scraphub.getAllEmployees.queryOptions({
      scraphubId: scraphubId,
    }),
  );
  const { data: kabadiwalas } = useSuspenseQuery(
    trpc.scraphub.getAllAssociatedKabadiwalas.queryOptions({
      scraphubId: scraphubId,
    }),
  );

  if (isError) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <div className="mb-2 text-lg font-semibold text-destructive">
            Error loading scraphub details
          </div>
          <p className="text-muted-foreground">
            Please try refreshing the page or contact support if the problem
            persists.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto min-w-full space-y-8 p-6">
      {/* Header Section */}
      <div className="flex flex-col gap-6 lg:flex-row lg:items-center lg:justify-between">
        <div className="space-y-2">
          <h1 className="font-jakarta text-3xl font-bold text-foreground">
            {data.name}
          </h1>
          <p className="text-muted-foreground">
            Scraphub ID: <span className="font-mono text-sm">{data.id}</span>
          </p>
        </div>

        {/* Actions Section */}
        <div className="flex flex-wrap gap-3">
          <button className="inline-flex items-center gap-2 rounded-lg bg-secondary px-4 py-2 font-medium text-secondary-foreground transition-colors duration-200 hover:bg-secondary/80">
            <Building2 className="h-4 w-4" />
            Edit Details
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Main Information Card */}
        <div className="flex flex-col gap-6 lg:col-span-2">
          <div className="rounded-xl border border-border bg-card p-6 shadow-sm">
            <div className="mb-6 flex items-center gap-3">
              <div className="rounded-lg bg-teal-100 p-2">
                <Building2 className="h-5 w-5 text-teal-700" />
              </div>
              <h2 className="font-jakarta text-xl font-semibold text-card-foreground">
                Scraphub Information
              </h2>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">
                    Name
                  </label>
                  <p className="text-base font-medium text-foreground">
                    {data.name}
                  </p>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 gap-4 border-t border-border pt-4 sm:grid-cols-2">
                <div className="rounded-lg bg-black-0 p-4 text-center">
                  <div className="text-2xl font-bold text-teal-700">
                    {employees.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Employees</div>
                </div>
                <div className="rounded-lg bg-black-0 p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {kabadiwalas.length}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Kabadiwalas
                  </div>
                </div>
                {/*
                <div className="rounded-lg bg-black-0 p-4 text-center">
                  <div className="text-2xl font-bold text-black-600">0</div>
                  <div className="text-sm text-muted-foreground">
                    Active Orders
                  </div>
                </div> */}
              </div>
            </div>
          </div>

          <div>
            <p className="font-jakarta text-xl font-semibold">Employees</p>
            <ScraphubEmployeesDataTable scraphubId={data.id} />
          </div>

          <div>
            <p className="font-jakarta text-xl font-semibold">Kabadiwalas</p>
            <ScraphubKabadiwalasDataTable scraphubId={data.id} />
          </div>
        </div>

        {/* Address Information Card */}
        <div className="space-y-6">
          <div className="rounded-xl border border-border bg-card p-6 shadow-sm">
            <div className="mb-6 flex items-center gap-3">
              <div className="rounded-lg bg-yellow-100 p-2">
                <MapPin className="h-5 w-5 text-yellow-700" />
              </div>
              <h2 className="font-jakarta text-xl font-semibold text-card-foreground">
                Address
              </h2>
            </div>

            {data.address ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">
                    Display Address
                  </label>
                  <p className="text-sm text-foreground">
                    {data.address.display}
                  </p>
                </div>

                {data.address.street && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-muted-foreground">
                      Street
                    </label>
                    <p className="text-sm text-foreground">
                      {data.address.street}
                    </p>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  {data.address.city && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-muted-foreground">
                        City
                      </label>
                      <p className="text-sm text-foreground">
                        {data.address.city}
                      </p>
                    </div>
                  )}
                  {data.address.state && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-muted-foreground">
                        State
                      </label>
                      <p className="text-sm text-foreground">
                        {data.address.state}
                      </p>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {data.address.country && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-muted-foreground">
                        Country
                      </label>
                      <p className="text-sm text-foreground">
                        {data.address.country}
                      </p>
                    </div>
                  )}
                  {data.address.postalCode && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-muted-foreground">
                        Postal Code
                      </label>
                      <p className="text-sm text-foreground">
                        {data.address.postalCode}
                      </p>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">
                    Landmark
                  </label>
                  <p className="text-sm text-foreground">
                    {data.address.landmark}
                  </p>
                </div>

                {data.address.coordinates && (
                  <div className="border-t border-border pt-4">
                    <label className="mb-2 block text-sm font-medium text-muted-foreground">
                      Coordinates
                    </label>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <span className="rounded bg-black-0 px-2 py-1 font-mono">
                        Lat: {data.address.coordinates.latitude}
                      </span>
                      <span className="rounded bg-black-0 px-2 py-1 font-mono">
                        Lng: {data.address.coordinates.longitude}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="py-8 text-center">
                <MapPin className="mx-auto mb-3 h-12 w-12 text-muted-foreground" />
                <p className="text-muted-foreground">
                  No address information available
                </p>
                <button className="mt-3 text-sm font-medium text-teal-600 hover:text-teal-700">
                  Add Address
                </button>
              </div>
            )}
          </div>

          {/* Quick Actions Card */}
          <div className="rounded-xl border border-border bg-card p-6 shadow-sm">
            <h3 className="mb-4 font-jakarta text-lg font-semibold text-card-foreground">
              Quick Actions
            </h3>
            <div className="space-y-3">
              {/* <button className="flex w-full items-center gap-3 rounded-lg bg-black-0 px-4 py-3 text-left transition-colors duration-200 hover:bg-black-50">
                <Users className="h-4 w-4 text-teal-600" />
                <span className="text-sm font-medium">Manage Employees</span>
              </button> */}
              {data.address?.coordinates && (
                <Link
                  target="_blank"
                  href={`https://google.com/maps?q=${data.address.coordinates.latitude},${data.address.coordinates.longitude}`}
                  className="flex w-full items-center gap-3 rounded-lg bg-black-0 px-4 py-3 text-left transition-colors duration-200 hover:bg-black-50"
                >
                  <Globe className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium">View on Map</span>
                  <ExternalLink className="h-4 w-4 text-yellow-600" />
                </Link>
              )}{" "}
              {data.phoneNumber && (
                <div className="flex w-full items-center gap-3 rounded-lg bg-black-0 px-4 py-3 text-left transition-colors duration-200 hover:bg-black-50">
                  <Phone className="h-4 w-4 text-black-600" />
                  <span className="text-sm font-medium">
                    Contact Info - {data.phoneNumber}
                  </span>
                  <CopyToClipboardButton text={data.phoneNumber} />{" "}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScraphubDetails;
