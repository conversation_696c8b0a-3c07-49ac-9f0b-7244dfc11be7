import crypto from "crypto";
import { NextRequest, NextResponse } from "next/server";

import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import {
  customerPaymentTransaction,
  kabadiwalaPaymentTransaction,
  payout,
  scraphubPaymentTransaction,
} from "@acme/db/schema";

import { env } from "~/env";

// Cashfree signature verification
function verifyCashfreeSignature(
  payload: string,
  signature: string,
  timestamp: string,
): boolean {
  try {
    // Create the expected signature using timestamp + payload
    const expectedSignature = crypto
      .createHmac("sha256", env.CASHFREE_SECRET_KEY)
      .update(timestamp + payload)
      .digest("base64");

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature),
    );
  } catch (error) {
    console.error("Signature verification error:", error);
    return false;
  }
}

// Handle payment webhook events for all user types
async function handlePaymentWebhook(webhookData: any) {
  try {
    const { order } = webhookData.data;

    if (!order || !order.order_id) {
      console.error("Invalid payment webhook data:", webhookData);
      throw new Error("Invalid payment data");
    }

    // Find the payment transaction record by Cashfree order ID
    let paymentRecord = null;
    let userType = null;

    // Check kabadiwala payment transactions
    paymentRecord = await db.query.kabadiwalaPaymentTransaction.findFirst({
      where: eq(kabadiwalaPaymentTransaction.cashfreeOrderId, order.order_id),
    });
    if (paymentRecord) userType = "KABADIWALA";

    // Check scraphub payment transactions if not found
    if (!paymentRecord) {
      paymentRecord = await db.query.scraphubPaymentTransaction.findFirst({
        where: eq(scraphubPaymentTransaction.cashfreeOrderId, order.order_id),
      });
      if (paymentRecord) userType = "SCRAPHUB";
    }

    // Check customer payment transactions if not found
    if (!paymentRecord) {
      paymentRecord = await db.query.customerPaymentTransaction.findFirst({
        where: eq(customerPaymentTransaction.cashfreeOrderId, order.order_id),
      });
      if (paymentRecord) userType = "CUSTOMER";
    }

    if (!paymentRecord) {
      console.warn(`Payment record not found for order_id: ${order.order_id}`);
      return {
        success: true,
        message: "Payment not found in system",
        orderId: order.order_id,
      };
    }

    // Map Cashfree payment status to our status
    let paymentStatus = "PENDING";
    if (order.order_status === "PAID") {
      paymentStatus = "SUCCESS";
    } else if (
      order.order_status === "EXPIRED" ||
      order.order_status === "CANCELLED"
    ) {
      paymentStatus = "FAILED";
    }

    // Update payment record based on user type
    const updateData = {
      status: paymentStatus as any,
      cashfreePaymentId: order.cf_order_id,
      gatewayMetadata: {
        ...paymentRecord.gatewayMetadata,
        webhookType: webhookData.type,
        webhookReceivedAt: new Date().toISOString(),
        orderStatus: order.order_status,
        paymentMethod: order.payment_method,
        orderAmount: order.order_amount,
        orderCurrency: order.order_currency,
        orderNote: order.order_note,
      },
    };

    if (userType === "KABADIWALA") {
      await db
        .update(kabadiwalaPaymentTransaction)
        .set(updateData)
        .where(eq(kabadiwalaPaymentTransaction.id, paymentRecord.id));
    } else if (userType === "SCRAPHUB") {
      await db
        .update(scraphubPaymentTransaction)
        .set(updateData)
        .where(eq(scraphubPaymentTransaction.id, paymentRecord.id));
    } else if (userType === "CUSTOMER") {
      await db
        .update(customerPaymentTransaction)
        .set(updateData)
        .where(eq(customerPaymentTransaction.id, paymentRecord.id));
    }

    console.log(
      `Updated ${userType} payment ${order.order_id} with status: ${paymentStatus}`,
    );

    // TODO: Add user-type specific processing for payments
    switch (userType) {
      case "KABADIWALA":
        // Handle kabadiwala payment completion
        // - Update wallet balance if successful
        // - Send notification to kabadiwala
        console.log(`Processed kabadiwala payment: ${order.order_id}`);
        break;

      case "SCRAPHUB":
        // Handle scraphub payment completion
        // - Update wallet balance if successful
        // - Send notification to scraphub
        console.log(`Processed scraphub payment: ${order.order_id}`);
        break;

      case "CUSTOMER":
        // Handle customer payment completion
        // - Update order status if successful
        // - Send notification to customer
        console.log(`Processed customer payment: ${order.order_id}`);
        break;
    }

    return {
      success: true,
      orderId: order.order_id,
      status: paymentStatus,
      userType,
    };
  } catch (error) {
    console.error("Error handling payment webhook:", error);
    throw error;
  }
}

// Handle transfer webhook events for all user types
async function handleTransferWebhook(webhookData: any) {
  try {
    const { transfer } = webhookData.data;

    if (!transfer || !transfer.transfer_id) {
      console.error("Invalid transfer webhook data:", webhookData);
      throw new Error("Invalid transfer data");
    }

    // Find the payout record
    const payoutRecord = await db.query.payout.findFirst({
      where: eq(payout.transferId, transfer.transfer_id),
    });

    if (!payoutRecord) {
      console.warn(
        `Payout record not found for transfer_id: ${transfer.transfer_id}`,
      );
      // Return success to avoid webhook retries for unknown transfers
      return {
        success: true,
        message: "Transfer not found in system",
        transferId: transfer.transfer_id,
      };
    }

    // Update payout record with webhook data
    await db
      .update(payout)
      .set({
        status: transfer.status,
        statusDescription: transfer.status_description,
        cfTransferId: transfer.cf_transfer_id,
        transferUtr: transfer.transfer_utr,
        transferServiceCharge: transfer.transfer_service_charge
          ? String(transfer.transfer_service_charge)
          : null,
        transferServiceTax: transfer.transfer_service_tax
          ? String(transfer.transfer_service_tax)
          : null,
        updatedAt: new Date(),
        processedAt: transfer.status === "SUCCESS" ? new Date() : null,
        gatewayMetadata: {
          ...payoutRecord.gatewayMetadata,
          webhookType: webhookData.type,
          webhookReceivedAt: new Date().toISOString(),
          transferMode: transfer.transfer_mode,
          beneficiaryDetails: transfer.beneficiary_details,
          addedOn: transfer.added_on,
          updatedOn: transfer.updated_on,
          processedBy: "admin-webhook",
        },
      })
      .where(eq(payout.transferId, transfer.transfer_id));

    console.log(
      `Updated ${payoutRecord.userType} payout ${transfer.transfer_id} with status: ${transfer.status}`,
    );

    // TODO: Add user-type specific processing
    switch (payoutRecord.userType) {
      case "SELLER":
        // Handle seller payout completion
        // - Send notification to seller
        // - Update seller dashboard
        console.log(`Processed seller payout: ${transfer.transfer_id}`);
        break;

      case "KABADIWALA":
        // Handle kabadiwala withdrawal completion
        // - Send notification to kabadiwala
        // - Update kabadiwala dashboard
        console.log(`Processed kabadiwala withdrawal: ${transfer.transfer_id}`);
        break;

      case "SCRAPHUB":
        // Handle scraphub withdrawal completion
        // - Send notification to scraphub
        // - Update scraphub dashboard
        // - Handle failed withdrawals (restore wallet balance if needed)
        console.log(`Processed scraphub withdrawal: ${transfer.transfer_id}`);
        break;
    }

    return {
      success: true,
      transferId: transfer.transfer_id,
      status: transfer.status,
      userType: payoutRecord.userType,
    };
  } catch (error) {
    console.error("Error handling transfer webhook:", error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get webhook headers - Cashfree uses these specific header names
    const signature = request.headers.get("x-webhook-signature");
    const timestamp = request.headers.get("x-webhook-timestamp");

    if (!signature || !timestamp) {
      console.error("Missing webhook headers:", {
        signature: !!signature,
        timestamp: !!timestamp,
      });
      return NextResponse.json(
        { error: "Missing required webhook headers" },
        { status: 400 },
      );
    }

    // Get request body
    const body = await request.json();
    const payload = JSON.stringify(body);

    // Verify webhook signature
    if (!verifyCashfreeSignature(payload, signature, timestamp)) {
      console.error("Invalid webhook signature");
      return NextResponse.json(
        { error: "Invalid webhook signature" },
        { status: 401 },
      );
    }

    // Parse webhook data
    const webhookData = body;
    console.log("Received Cashfree webhook (admin):", {
      type: webhookData.type,
      timestamp,
    });

    // Handle payment-related webhooks (collections)
    if (
      webhookData.type &&
      (webhookData.type === "PAYMENT_SUCCESS_WEBHOOK" ||
        webhookData.type === "PAYMENT_FAILED_WEBHOOK" ||
        webhookData.type === "PAYMENT_USER_DROPPED_WEBHOOK")
    ) {
      const result = await handlePaymentWebhook(webhookData);
      return NextResponse.json(result);
    }

    // Handle transfer-related webhooks (payouts)
    if (
      webhookData.type &&
      (webhookData.type === "TRANSFER_SUCCESS" ||
        webhookData.type === "TRANSFER_FAILED" ||
        webhookData.type === "TRANSFER_REVERSED")
    ) {
      const result = await handleTransferWebhook(webhookData);
      return NextResponse.json(result);
    }

    // Handle other webhook types
    console.log("Received other webhook type:", webhookData.type);

    // Handle beneficiary webhooks
    if (
      webhookData.type === "BENEFICIARY_VERIFIED" ||
      webhookData.type === "BENEFICIARY_FAILED"
    ) {
      console.log(
        `Beneficiary ${webhookData.type.toLowerCase()}:`,
        webhookData.data,
      );
      // TODO: Update beneficiary status in respective user tables
      // - Update seller.beneficiaryStatus
      // - Update kabadiwala.beneficiaryStatus
      // - Update scraphub.beneficiaryStatus

      return NextResponse.json({
        success: true,
        message: "Beneficiary webhook processed",
        type: webhookData.type,
      });
    }

    // Handle settlement webhooks
    if (
      webhookData.type === "SETTLEMENT_SUCCESS" ||
      webhookData.type === "SETTLEMENT_FAILED"
    ) {
      console.log(
        `Settlement ${webhookData.type.toLowerCase()}:`,
        webhookData.data,
      );
      // TODO: Handle settlement events if needed

      return NextResponse.json({
        success: true,
        message: "Settlement webhook processed",
        type: webhookData.type,
      });
    }

    // Handle unhandled webhook types
    console.log("Received unhandled webhook type:", webhookData.type);

    return NextResponse.json({
      success: true,
      message: "Webhook received but not processed",
      type: webhookData.type,
    });
  } catch (error) {
    console.error("Webhook processing error:", error);

    // Return 500 for server errors to trigger webhook retries
    return NextResponse.json(
      { error: "Internal server error", processed: false },
      { status: 500 },
    );
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "cashfree-webhook-handler",
  });
}
