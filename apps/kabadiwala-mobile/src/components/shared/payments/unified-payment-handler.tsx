import { Alert } from "react-native";
import {
  CFPaymentGatewayService,
  CFSession,
} from "react-native-cashfree-pg-sdk";
import { Env } from "@/lib/env";

export interface PaymentOptions {
  orderId: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  customerEmail?: string;
  customerPhone?: string;
  customerName?: string;
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  orderId: string;
  signature?: string;
  error?: string;
}

export class UnifiedPaymentHandler {
  private static async handleCashfreePayment(
    options: PaymentOptions,
  ): Promise<PaymentResult> {
    try {
      // Initialize Cashfree SDK
      CFPaymentGatewayService.setCallback({
        onVerify: (orderID: string) => {
          console.log("Cashfree payment verification for order:", orderID);
        },
        onError: (error: any, orderID: string) => {
          console.error("Cashfree payment error for order:", orderID, error);
        },
      });

      // Create Cashfree session
      const session = new CFSession(
        options.orderId,
        options.amount,
        options.currency,
        Env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT || "sandbox",
      );

      // Set customer details
      if (
        options.customerEmail ||
        options.customerPhone ||
        options.customerName
      ) {
        session.setCustomerDetails(
          options.customerName || "",
          options.customerEmail || "",
          options.customerPhone || "",
        );
      }

      // Start payment
      const result = await CFPaymentGatewayService.doPayment(session);

      if (result && result.orderStatus === "PAID") {
        return {
          success: true,
          paymentId: result.txnId,
          orderId: options.orderId,
        };
      } else {
        return {
          success: false,
          orderId: options.orderId,
          error: result?.txnMsg || "Payment failed",
        };
      }
    } catch (error: any) {
      console.error("Cashfree payment failed:", error);
      return {
        success: false,
        orderId: options.orderId,
        error: error.message || "Payment failed",
      };
    }
  }

  public static async processPayment(
    options: PaymentOptions,
  ): Promise<PaymentResult> {
    console.log("Processing payment with Cashfree gateway");

    try {
      const result = await this.handleCashfreePayment(options);

      if (!result.success) {
        // Show error alert
        Alert.alert(
          "Payment Failed",
          result.error || "An error occurred during payment processing",
          [{ text: "OK" }],
        );
      }

      return result;
    } catch (error: any) {
      console.error("Payment processing error:", error);

      const errorResult: PaymentResult = {
        success: false,
        orderId: options.orderId,
        error: error.message || "Payment processing failed",
      };

      Alert.alert("Payment Error", errorResult.error, [{ text: "OK" }]);

      return errorResult;
    }
  }

  public static showPaymentInfo() {
    Alert.alert("Payment Gateway", "Currently using: CASHFREE", [
      { text: "OK" },
    ]);
  }
}

// Hook for using unified payment handler in components
export const useUnifiedPayment = () => {
  const processPayment = async (
    options: PaymentOptions,
  ): Promise<PaymentResult> => {
    return UnifiedPaymentHandler.processPayment(options);
  };

  const showGatewayInfo = () => {
    UnifiedPaymentHandler.showPaymentInfo();
  };

  const getCurrentGateway = () => {
    return "CASHFREE";
  };

  return {
    processPayment,
    showGatewayInfo,
    getCurrentGateway,
  };
};
