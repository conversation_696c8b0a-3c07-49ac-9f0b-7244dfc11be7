// Simple payment configuration for Cashfree only
export const getPaymentGatewayInfo = () => ({
  primary: "CASHFREE",
  fallback: null,
  environment: "sandbox", // This would come from env in real implementation
});

// Helper function to get current gateway type
export const getCurrentPaymentGateway = () => "CASHFREE";

// Helper function to check if fallback is available
export const hasFallbackGateway = () => false;
