import { createId } from "@paralleldrive/cuid2";
import { relations } from "drizzle-orm";
import {
  boolean,
  json,
  jsonb,
  numeric,
  pgEnum,
  pgTable,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

import { category, kabadiwala, order, orderItem } from "./old-schemas";
import { paymentGatewayEnum } from "./payment-enum";

export const organizationTypeEnum = pgEnum("organizationTypeEnum", [
  "PRIVATE_LIMITED",
  "PUBLIC_LIMITED",
  "PARTNERSHIP",
  "PROPRIETORSHIP",
  "LLP",
  "TRUST",
  "SOCIETY",
  "OTHER",
]);

export const scraphubOnboardingStepEnum = pgEnum("scraphubOnboardingStepEnum", [
  "EMAIL_VERIFIED",
  "ORGANIZATION_DETAILS",
  "LEGAL_DOCUMENTS",
  "DIRECTOR_INFO",
  "ADDRESS",
  "COMPLETED",
]);

export const scraphubApprovalStatusEnum = pgEnum("scraphubApprovalStatusEnum", [
  "PENDING",
  "APPROVED",
  "REJECTED",
]);

export const scraphubEmployeeRoleEnum = pgEnum("scraphubEmployeeRoleEnum", [
  "ADMIN",
  "EMPLOYEE",
]);

export const scraphub = pgTable("scraphub", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  name: text("name").notNull(),
  phoneNumber: text("phoneNumber"),
  // New fields for organization details
  organizationType: organizationTypeEnum("organizationType"),
  cinNumber: text("cinNumber"),
  cinCertificateFileKey: text("cinCertificateFileKey"),
  gstNumber: text("gstNumber"),
  gstCertificateFileKey: text("gstCertificateFileKey"),
  panNumber: text("panNumber"),
  panCertificateFileKey: text("panCertificateFileKey"),
  directorName: text("directorName"),
  directorAadharFileKey: text("directorAadharFileKey"),
  registeredOfficeAddress: text("registeredOfficeAddress"),
  communicationAddress: text("communicationAddress"),

  // Admin approval and wallet fields
  adminApprovalStatus: scraphubApprovalStatusEnum("adminApprovalStatus")
    .default("PENDING")
    .notNull(),
  adminApprovedAt: timestamp("adminApprovedAt", {
    mode: "date",
    withTimezone: true,
  }),
  adminApprovedBy: text("adminApprovedBy"), // Admin ID who approved
  adminRejectionReason: text("adminRejectionReason"),
  walletBalance: numeric("walletBalance").default("0").notNull(),
  cashfreeBeneficiaryId: text("cashfreeBeneficiaryId").unique(),
  beneficiaryStatus: text("beneficiaryStatus"), // VERIFIED, INVALID, etc.
  beneficiaryCreatedAt: timestamp("beneficiaryCreatedAt"),
  beneficiaryUpdatedAt: timestamp("beneficiaryUpdatedAt"),

  // Onboarding tracking
  onboardingStep:
    scraphubOnboardingStepEnum("onboardingStep").default("EMAIL_VERIFIED"),
  signupCompleted: boolean("signupCompleted").default(false).notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const scraphubRelations = relations(scraphub, ({ one, many }) => ({
  address: one(scraphubAddress, {
    fields: [scraphub.id],
    references: [scraphubAddress.scraphubId],
  }),
  employees: many(scraphubEmployee),
  kabadiwalas: many(kabadiwala),
  transactions: many(scraphubPaymentTransaction),
  inventory: one(scraphubInventory, {
    fields: [scraphub.id],
    references: [scraphubInventory.scraphubId],
  }),
}));

export const scraphubAddress = pgTable("scraphubAddress", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  display: text("display").notNull(),
  street: text("street"),
  city: text("city"),
  state: text("state"),
  country: text("country"),
  postalCode: text("postalCode"),
  coordinates: json("coordinates").$type<{
    latitude: number;
    longitude: number;
  }>(),
  // localAddress: text("localAddress").notNull(),
  landmark: text("landmark").notNull(),
  scraphubId: varchar("scraphubId", { length: 128 })
    .references(() => scraphub.id, { onDelete: "cascade" })
    .notNull()
    .unique(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const scraphubAddressRelations = relations(
  scraphubAddress,
  ({ one }) => ({
    scraphub: one(scraphub, {
      fields: [scraphubAddress.scraphubId],
      references: [scraphub.id],
    }),
  }),
);

export const scraphubEmployee = pgTable("scraphubEmployee", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  scraphubId: varchar("scraphubId", { length: 128 }).references(
    () => scraphub.id,
    { onDelete: "cascade" },
  ),
  name: text("name").notNull(),
  email: text("email").notNull(),
  emailVerified: boolean("emailVerified").default(false).notNull(),
  role: scraphubEmployeeRoleEnum("role").default("ADMIN").notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const scraphubEmployeeRelations = relations(
  scraphubEmployee,
  ({ one, many }) => ({
    scraphub: one(scraphub, {
      fields: [scraphubEmployee.scraphubId],
      references: [scraphub.id],
    }),
    orders: many(order),
  }),
);

export const scraphubEmployeeSession = pgTable("scraphubEmployeeSession", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  expiresAt: timestamp("expiresAt").notNull(),
  token: text("token").notNull().unique(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  ipAddress: text("ipAddress"),
  userAgent: text("userAgent"),
  scraphubEmployeeId: text("scraphubEmployeeId")
    .notNull()
    .references(() => scraphubEmployee.id, { onDelete: "cascade" }),
});

export const scraphubEmployeeAccount = pgTable("scraphubEmployeeAccount", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  accountId: text("accountId").notNull(),
  providerId: text("providerId").notNull(),
  scraphubEmployeeId: text("scraphubEmployeeId")
    .notNull()
    .references(() => scraphubEmployee.id, { onDelete: "cascade" }),
  accessToken: text("accessToken"),
  refreshToken: text("refreshToken"),
  idToken: text("idToken"),
  accessTokenExpiresAt: timestamp("accessTokenExpiresAt"),
  refreshTokenExpiresAt: timestamp("refreshTokenExpiresAt"),
  scope: text("scope"),
  password: text("password"),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

export const scraphubEmployeeVerification = pgTable(
  "scraphubEmployeeVerification",
  {
    id: varchar("id", { length: 128 })
      .$defaultFn(() => createId())
      .primaryKey(),
    identifier: text("identifier").notNull(),
    value: text("value").notNull(),
    expiresAt: timestamp("expiresAt").notNull(),
    createdAt: timestamp("createdAt"),
    updatedAt: timestamp("updatedAt"),
  },
);

// ScrapHub payment transaction enums
export const scraphubPaymentStatusEnum = pgEnum("scraphubPaymentStatusEnum", [
  "PENDING",
  "SUCCESS",
  "FAILED",
]);

export const scraphubTransactionTypeEnum = pgEnum(
  "scraphubTransactionTypeEnum",
  ["CREDIT", "DEBIT"],
);

export const scraphubTransactionForEnum = pgEnum("scraphubTransactionForEnum", [
  "DEPOSIT",
  "WITHDRAWAL",
  "ORDER_PAYMENT",
  "REFUND",
  "OTHER",
]);

// ScrapHub payment transaction table
export const scraphubPaymentTransaction = pgTable(
  "scraphubPaymentTransaction",
  {
    id: varchar("id", { length: 128 })
      .$defaultFn(() => createId())
      .primaryKey(),
    scraphubId: text("scraphubId")
      .notNull()
      .references(() => scraphub.id, { onDelete: "cascade" }),
    amount: numeric("amount").notNull(),

    // Payment gateway information
    paymentGateway: paymentGatewayEnum("paymentGateway")
      .notNull()
      .default("RAZORPAY"),

    // Razorpay specific fields
    razorpayOrderId: text("razorpayOrderId"),
    razorpayPaymentId: text("razorpayPaymentId"),

    // Cashfree specific fields
    cashfreeOrderId: text("cashfreeOrderId"),
    cashfreePaymentId: text("cashfreePaymentId"),

    // Gateway metadata for storing additional gateway-specific information
    gatewayMetadata: jsonb("gatewayMetadata").$type<Record<string, any>>(),

    currency: text("currency").default("INR"),
    status: scraphubPaymentStatusEnum("status").notNull(),
    transactionType: scraphubTransactionTypeEnum("transactionType").notNull(),
    transactionFor: scraphubTransactionForEnum("transactionFor").notNull(),

    // Additional fields
    description: text("description"),
    adminApprovedBy: text("adminApprovedBy"), // Admin ID who approved the transaction

    createdAt: timestamp("createdAt").defaultNow().notNull(),
    updatedAt: timestamp("updatedAt", {
      mode: "date",
      withTimezone: true,
    }).$onUpdate(() => new Date()),
  },
);

export const scraphubPaymentTransactionRelations = relations(
  scraphubPaymentTransaction,
  ({ one }) => ({
    scraphub: one(scraphub, {
      fields: [scraphubPaymentTransaction.scraphubId],
      references: [scraphub.id],
    }),
  }),
);

// ScrapHub Inventory System
export const scraphubInventory = pgTable("scraphubInventory", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  scraphubId: varchar("scraphubId", { length: 128 })
    .references(() => scraphub.id, { onDelete: "cascade" })
    .notNull(),
  totalValue: numeric("totalValue").default("0").notNull(),
  totalItems: numeric("totalItems").default("0").notNull(),
  lastUpdatedAt: timestamp("lastUpdatedAt", {
    mode: "date",
    withTimezone: true,
  })
    .defaultNow()
    .notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const scraphubInventoryItem = pgTable("scraphubInventoryItem", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  inventoryId: varchar("inventoryId", { length: 128 })
    .references(() => scraphubInventory.id, { onDelete: "cascade" })
    .notNull(),
  categoryId: varchar("categoryId", { length: 128 })
    .references(() => category.id)
    .notNull(),
  orderId: varchar("orderId", { length: 128 })
    .references(() => order.id)
    .notNull(),
  orderItemId: varchar("orderItemId", { length: 128 })
    .references(() => orderItem.id)
    .notNull(),
  quantity: numeric("quantity").notNull(),
  rate: numeric("rate").notNull(),
  totalValue: numeric("totalValue").notNull(),
  addedAt: timestamp("addedAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const scraphubInventoryRelations = relations(
  scraphubInventory,
  ({ one, many }) => ({
    scraphub: one(scraphub, {
      fields: [scraphubInventory.scraphubId],
      references: [scraphub.id],
    }),
    items: many(scraphubInventoryItem),
  }),
);

export const scraphubInventoryItemRelations = relations(
  scraphubInventoryItem,
  ({ one }) => ({
    inventory: one(scraphubInventory, {
      fields: [scraphubInventoryItem.inventoryId],
      references: [scraphubInventory.id],
    }),
    category: one(category, {
      fields: [scraphubInventoryItem.categoryId],
      references: [category.id],
    }),
    order: one(order, {
      fields: [scraphubInventoryItem.orderId],
      references: [order.id],
    }),
    orderItem: one(orderItem, {
      fields: [scraphubInventoryItem.orderItemId],
      references: [orderItem.id],
    }),
  }),
);
