import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

import { env as authEnv } from "@acme/kabadiwala-auth/env";

export const env = createEnv({
  extends: [authEnv],
  shared: {
    NODE_ENV: z
      .enum(["development", "production", "test"])
      .default("development"),
  },
  /**
   * Specify your server-side environment variables schema here.
   * This way you can ensure the app isn't built with invalid env vars.
   */
  server: {
    POSTGRES_URL: z.string(),
    UPLOADTHING_TOKEN: z.string().nonempty(),

    // Cashfree configuration
    CASHFREE_APP_ID: z.string().nonempty(),
    CASHFREE_SECRET_KEY: z.string().nonempty(),

    DL_VERIFICATION_API_KEY: z.string().nonempty(),
    GETSTREAM_API_KEY: z.string().nonempty(),
    GETSTREAM_API_SECRET: z.string().nonempty(),
  },

  client: {
    NEXT_PUBLIC_COMET_CHAT_APP_ID: z.string().nonempty(),
    NEXT_PUBLIC_COMET_CHAT_REGION: z.string().nonempty(),
    NEXT_PUBLIC_ONESIGNAL_KABADIWALA_APP_ID: z.string().nonempty(),
    NEXT_PUBLIC_CASHFREE_ENVIRONMENT: z
      .enum(["sandbox", "production"])
      .default("sandbox"),
  },

  /**
   * Destructure all variables from `process.env` to make sure they aren't tree-shaken away.
   */
  experimental__runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_COMET_CHAT_APP_ID: process.env.NEXT_PUBLIC_COMET_CHAT_APP_ID,
    NEXT_PUBLIC_COMET_CHAT_REGION: process.env.NEXT_PUBLIC_COMET_CHAT_REGION,
    NEXT_PUBLIC_ONESIGNAL_KABADIWALA_APP_ID:
      process.env.NEXT_PUBLIC_ONESIGNAL_KABADIWALA_APP_ID,
    NEXT_PUBLIC_CASHFREE_ENVIRONMENT:
      process.env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT,
    // NEXT_PUBLIC_CLIENTVAR: process.env.NEXT_PUBLIC_CLIENTVAR,
  },
  skipValidation:
    !!process.env.CI || process.env.npm_lifecycle_event === "lint",
});
