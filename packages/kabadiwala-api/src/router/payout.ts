import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  Cashfree,
  createBankBeneficiaryRequest,
  createSimpleTransferRequest,
  createTransferId,
  createUPIBeneficiaryRequest,
  validateBeneficiaryRequest,
  validateTransferRequest,
} from "@acme/cashfree-sdk";
import { eq } from "@acme/db";
import {
  kabadiwala,
  kabadiwalaPaymentTransaction,
  payout,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../env";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Initialize Cashfree client for payouts
const cashfree = new Cashfree({
  appId: env.CASHFREE_APP_ID,
  secretKey: env.CASHFREE_SECRET_KEY,
  environment: env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT || "sandbox",
});

// Schemas
const CreateContactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email().optional(),
  contact: z.string().optional(),
  type: z.enum(["customer", "vendor", "employee"]).default("customer"),
  reference_id: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

const CreateFundAccountSchema = z
  .object({
    contact_id: z.string(),
    account_type: z.enum(["bank_account", "vpa"]),
    bank_account: z
      .object({
        name: z.string(),
        account_number: z.string(),
        ifsc: z.string(),
      })
      .optional(),
    vpa: z
      .object({
        address: z.string(),
      })
      .optional(),
  })
  .refine(
    (data) => {
      if (data.account_type === "bank_account" && !data.bank_account) {
        return false;
      }
      if (data.account_type === "vpa" && !data.vpa) {
        return false;
      }
      return true;
    },
    {
      message: "Account details must match the account type",
    },
  );

const CreatePayoutSchema = z.object({
  fund_account_id: z.string(),
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().default("INR"),
  mode: z.enum(["IMPS", "NEFT", "RTGS", "UPI"]),
  purpose: z.string(),
  reference_id: z.string().optional(),
  narration: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

export const payoutRouter = createTRPCRouter({
  // Get payout gateway info
  getGatewayInfo: protectedProcedure.query(async () => {
    return {
      primary: "CASHFREE",
      fallback: null,
      environment: env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT || "sandbox",
    };
  }),

  // Contact management (Cashfree uses beneficiaries instead of contacts)
  createContact: protectedProcedure
    .input(CreateContactSchema)
    .mutation(async ({ input }) => {
      // For Cashfree, contacts are just metadata - we create beneficiaries separately
      // This is a placeholder that returns the contact info
      return {
        id: `contact_${Date.now()}`,
        name: input.name,
        email: input.email,
        contact: input.contact,
        type: input.type,
      };
    }),

  // Create kabadiwala beneficiary for withdrawals
  createKabadiwalaWithdrawalAccount: protectedProcedure
    .input(
      z.object({
        accountType: z.enum(["bank_account", "vpa"]),
        bankAccount: z.string().optional(),
        ifsc: z.string().optional(),
        vpa: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const kabadiwalaId = ctx.session.user.id;

      // Get kabadiwala details
      const { data: kabadiwalaData, err } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, kabadiwalaId),
          columns: {
            name: true,
            email: true,
            phoneNumber: true,
            cashfreeBeneficiaryId: true,
          },
        }),
      );

      if (err || !kabadiwalaData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Kabadiwala not found",
        });
      }

      // Check if beneficiary already exists
      if (kabadiwalaData.cashfreeBeneficiaryId) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Withdrawal account already exists",
        });
      }

      const beneficiaryId = `kabadiwala_${kabadiwalaId}`;

      let beneficiaryRequest;
      if (input.accountType === "vpa" && input.vpa) {
        beneficiaryRequest = createUPIBeneficiaryRequest({
          beneficiaryId,
          name: kabadiwalaData.name,
          vpa: input.vpa,
          email: kabadiwalaData.email || undefined,
          phone: kabadiwalaData.phoneNumber || undefined,
        });
      } else if (
        input.accountType === "bank_account" &&
        input.bankAccount &&
        input.ifsc
      ) {
        beneficiaryRequest = createBankBeneficiaryRequest({
          beneficiaryId,
          name: kabadiwalaData.name,
          bankAccount: input.bankAccount,
          ifsc: input.ifsc,
          email: kabadiwalaData.email || undefined,
          phone: kabadiwalaData.phoneNumber || undefined,
        });
      } else {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid account details provided",
        });
      }

      // Validate request
      const errors = validateBeneficiaryRequest(beneficiaryRequest);
      if (errors.length > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Validation failed: ${errors.join(", ")}`,
        });
      }

      const { data, error } =
        await cashfree.createBeneficiary(beneficiaryRequest);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to create withdrawal account",
        });
      }

      // Update kabadiwala with beneficiary ID
      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            cashfreeBeneficiaryId: data?.beneficiary_id,
            beneficiaryStatus: data?.beneficiary_status,
            beneficiaryCreatedAt: new Date(),
            beneficiaryUpdatedAt: new Date(),
          })
          .where(eq(kabadiwala.id, kabadiwalaId)),
      );

      if (updateErr) {
        console.error(
          "Failed to update kabadiwala with beneficiary ID:",
          updateErr,
        );
      }

      return {
        beneficiaryId: data?.beneficiary_id,
        status: data?.beneficiary_status,
        accountType: input.accountType,
      };
    }),

  // Fund account management (Cashfree uses beneficiaries with payment details)
  createFundAccount: protectedProcedure
    .input(CreateFundAccountSchema)
    .mutation(async ({ input }) => {
      // For Cashfree, we create a beneficiary with payment details
      const beneficiaryData: any = {
        bene_id: `fund_${Date.now()}`,
        name: "Fund Account", // This would need to be passed from input or fetched from contact
      };

      // Add payment method details based on account type
      if (input.account_type === "bank_account" && input.bank_account) {
        beneficiaryData.bank_account = input.bank_account.account_number;
        beneficiaryData.ifsc = input.bank_account.ifsc;
      } else if (input.account_type === "vpa" && input.vpa) {
        beneficiaryData.vpa = input.vpa.address;
      }

      const { data, error } = await cashfree.createBeneficiary(beneficiaryData);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to create fund account",
        });
      }

      return {
        id: data?.beneficiary_id,
        contact_id: input.contact_id,
        account_type: input.account_type,
        active: true,
      };
    }),

  getAllFundAccounts: protectedProcedure
    .input(z.object({ contact_id: z.string() }))
    .query(async ({ input }) => {
      // For Cashfree, we would list beneficiaries
      // For now, return empty array as we don't have a direct mapping
      return [];
    }),

  deactivateFundAccount: protectedProcedure
    .input(z.object({ fund_account_id: z.string() }))
    .mutation(async ({ input }) => {
      // For Cashfree, we would remove/deactivate a beneficiary
      // For now, return success as we don't have direct mapping
      return { success: true };
    }),

  // Payout operations
  createPayout: protectedProcedure
    .input(CreatePayoutSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if kabadiwala has sufficient balance
      const { data: kabadiwalaData, err: kabadiwalaErr } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, ctx.session.user.id),
          columns: {
            walletBalance: true,
          },
        }),
      );

      if (kabadiwalaErr || !kabadiwalaData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Kabadiwala not found",
        });
      }

      const currentBalance = Number(kabadiwalaData.walletBalance);
      if (currentBalance < input.amount) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Insufficient wallet balance",
        });
      }

      // Generate unique transfer ID
      const transferId = createTransferId(
        "kabadiwala_withdrawal",
        `${ctx.session.user.id}_${Date.now()}`,
      );

      // Create transfer request using V2 API
      const transferRequest = createSimpleTransferRequest({
        transferId,
        amount: input.amount,
        beneficiaryId: input.fund_account_id, // This should be the kabadiwala's beneficiary ID
        transferMode: input.mode.toLowerCase() as any,
        remarks: input.narration || input.purpose,
      });

      // Validate request
      const validationErrors = validateTransferRequest(transferRequest);
      if (validationErrors.length > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Validation failed: ${validationErrors.join(", ")}`,
        });
      }

      const { data, error } = await cashfree.createTransfer(transferRequest);

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to create payout",
        });
      }

      // Deduct amount from wallet balance
      const { err: walletUpdateErr } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            walletBalance: (currentBalance - input.amount).toString(),
          })
          .where(eq(kabadiwala.id, ctx.session.user.id)),
      );

      if (walletUpdateErr) {
        console.error("Failed to update wallet balance:", walletUpdateErr);
      }

      // Record payout in unified payout table
      const { err: payoutInsertErr } = await tryCatch(
        ctx.db.insert(payout).values({
          transferId,
          cfTransferId: data?.cf_transfer_id,
          userType: "KABADIWALA",
          userId: ctx.session.user.id,
          beneficiaryId: input.fund_account_id,
          amount: input.amount.toString(),
          currency: input.currency,
          payoutType: "WALLET_WITHDRAWAL",
          transferMode: input.mode.toLowerCase(),
          status: data?.status || "RECEIVED",
          remarks: input.narration || input.purpose,
          purpose: input.purpose,
          gatewayMetadata: {
            originalTransferId: transferId,
            mode: input.mode,
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      );

      // Also record in legacy transaction table for backward compatibility
      const { err: insertErr } = await tryCatch(
        ctx.db.insert(kabadiwalaPaymentTransaction).values({
          kabadiwalaId: ctx.session.user.id,
          amount: input.amount.toString(),
          paymentGateway: "CASHFREE",
          status: "PENDING",
          currency: input.currency,
          transactionFor: "WALLET_WITHDRAWAL",
          transactionType: "DEBIT",
          gatewayMetadata: {
            transfer_id: transferId,
            cf_transfer_id: data?.cf_transfer_id,
            fund_account_id: input.fund_account_id,
            purpose: input.purpose,
            mode: input.mode,
          },
        }),
      );

      if (insertErr || payoutInsertErr) {
        console.error(
          "Failed to record payout transaction:",
          insertErr || payoutInsertErr,
        );
      }

      return {
        id: transferId,
        transfer_id: data?.transfer_id,
        cf_transfer_id: data?.cf_transfer_id,
        status: data?.status,
        amount: data?.transfer_amount,
      };
    }),

  getPayoutStatus: protectedProcedure
    .input(z.object({ payout_id: z.string() }))
    .query(async ({ input }) => {
      // Get payout status from Cashfree
      // const { data, error } = await cashfree.getPayoutStatus(input.payout_id);

      // if (error) {
      //   throw new TRPCError({
      //     code: "INTERNAL_SERVER_ERROR",
      //     message: error.message || "Failed to get payout status",
      //   });
      // }

      return {}; // data;
    }),

  // Validation (Cashfree doesn't have direct fund account validation)
  validateFundAccount: protectedProcedure
    .input(
      z.object({
        fund_account_id: z.string(),
        amount: z.number().positive(),
        currency: z.string().default("INR"),
      }),
    )
    .mutation(async ({ input }) => {
      // For Cashfree, we can't validate fund accounts directly
      // Return success for now
      return {
        success: true,
        message: "Fund account validation not available with Cashfree",
      };
    }),

  // Get kabadiwala wallet balance
  getWalletBalance: protectedProcedure.query(async ({ ctx }) => {
    const { data: kabadiwalaData, err } = await tryCatch(
      ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        columns: {
          walletBalance: true,
        },
      }),
    );

    if (err || !kabadiwalaData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Kabadiwala not found",
      });
    }

    return {
      balance: Number(kabadiwalaData.walletBalance),
      currency: "INR",
    };
  }),

  // Trigger seller payout (called when kabadiwala completes order)
  triggerSellerPayout: protectedProcedure
    .input(
      z.object({
        sellerId: z.string(),
        amount: z.number().min(1),
        orderId: z.string(),
        remarks: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const transferId = createTransferId(
          "seller_order_payout",
          `${input.sellerId}_${input.orderId}_${Date.now()}`,
        );

        // Create transfer request - this assumes seller beneficiary already exists
        const transferRequest = createSimpleTransferRequest({
          transferId,
          amount: input.amount,
          beneficiaryId: `scraplo_seller_${input.sellerId}`, // Assuming this format
          transferMode: "imps",
          remarks: input.remarks || `Order payout for order ${input.orderId}`,
        });

        // Validate request
        const validationErrors = validateTransferRequest(transferRequest);
        if (validationErrors.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Validation failed: ${validationErrors.join(", ")}`,
          });
        }

        const { data, error } = await cashfree.createTransfer(transferRequest);

        if (error) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to initiate seller payout",
          });
        }

        // Record payout in unified payout table
        const { err: payoutInsertErr } = await tryCatch(
          ctx.db.insert(payout).values({
            transferId,
            cfTransferId: data?.cf_transfer_id,
            userType: "SELLER",
            userId: input.sellerId,
            beneficiaryId: `scraplo_seller_${input.sellerId}`,
            amount: input.amount.toString(),
            currency: "INR",
            payoutType: "ORDER_PAYOUT",
            transferMode: "imps",
            status: data?.status || "RECEIVED",
            remarks: input.remarks,
            purpose: `Order payout for order ${input.orderId}`,
            gatewayMetadata: {
              orderId: input.orderId,
              initiatedBy: "KABADIWALA",
              kabadiwalaId: ctx.session.user.id,
            },
            createdAt: new Date(),
            updatedAt: new Date(),
          }),
        );

        if (payoutInsertErr) {
          console.error("Failed to record seller payout:", payoutInsertErr);
        }

        return {
          transferId: data?.transfer_id,
          status: data?.status,
          amount: data?.transfer_amount,
        };
      } catch (error) {
        console.error("Seller payout trigger error:", error);
        throw error;
      }
    }),
});
