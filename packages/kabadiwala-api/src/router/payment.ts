import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { Cashfree } from "@acme/cashfree-sdk";
import { eq } from "@acme/db";
import {
  kabadiwala,
  kabadiwalaPaymentTransaction,
  order,
} from "@acme/db/schema";
import {
  AddMoneySchema,
  VerifyPaymentSchema,
} from "@acme/validators/kabadiwala";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../env";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Initialize Cashfree client
const cashfree = new Cashfree({
  appId: env.CASHFREE_APP_ID,
  secretKey: env.CASHFREE_SECRET_KEY,
  environment: env.NEXT_PUBLIC_CASHFREE_ENVIRONMENT || "sandbox",
});

export const paymentRouter = createTRPCRouter({
  createOrder: protectedProcedure
    .input(AddMoneySchema)
    .mutation(async ({ ctx, input }) => {
      // Create Cashfree order directly
      const cashfreeOrderId = `wallet_topup_${Date.now()}`;
      const customerId = `kabadiwala_${ctx.session.user.id}`;

      const { data: kabadiwalaData, err: kabadiwalaErr } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, ctx.session.user.id),
          columns: {
            phoneNumber: true,
          },
        }),
      );

      const orderRequest = {
        order_id: cashfreeOrderId,
        order_amount: input.amount,
        order_currency: input.currency,
        customer_details: {
          customer_id: customerId,
          customer_name: ctx.session.user.name || "Kabadiwala",
          customer_email: ctx.session.user.email || "",
          customer_phone: kabadiwalaData?.phoneNumber ?? "",
        },
        order_note: `Wallet topup of ₹${input.amount.toLocaleString()}`,
        order_tags: {
          customer_id: ctx.session.user.id,
          transaction_for: "WALLET_TOPUP",
        },
      };

      const { data: orderData, error: orderError } =
        await cashfree.createOrder(orderRequest);

      if (orderError || !orderData) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create order",
        });
      }

      // Prepare transaction data for Cashfree
      const transactionData = {
        kabadiwalaId: ctx.session.user.id,
        amount: input.amount.toString(),
        paymentGateway: "CASHFREE" as const,
        status: "PENDING" as const,
        currency: input.currency,
        transactionFor: "WALLET_TOPUP" as const,
        transactionType: "CREDIT" as const,
        razorpayOrderId: "",
        cashfreeOrderId: cashfreeOrderId,
      };

      const { data: insertData, err: insertErr } = await tryCatch(
        ctx.db
          .insert(kabadiwalaPaymentTransaction)
          .values(transactionData)
          .returning(),
      );

      const transactionId = insertData?.[0]?.id;

      if (insertErr || !transactionId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to insert payment transaction",
        });
      }

      return {
        orderId: cashfreeOrderId,
        transactionId: transactionId,
        name: "Add Money",
        amount: (orderData.order_amount || input.amount) * 100, // Convert to paise for frontend compatibility
        currency: orderData.order_currency || input.currency,
        description: orderData.order_note || "Add Money to Wallet",
      };
    }),

  verifyPayment: protectedProcedure
    .input(VerifyPaymentSchema)
    .mutation(async ({ ctx, input }) => {
      const { transactionId } = input;

      // Get transaction details
      const { data: transaction, err: transactionErr } = await tryCatch(
        ctx.db.query.kabadiwalaPaymentTransaction.findFirst({
          where: eq(kabadiwalaPaymentTransaction.id, transactionId),
        }),
      );

      if (transactionErr || !transaction) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Transaction not found",
        });
      }

      // Verify Cashfree payment
      const { cashfreeOrderId, cashfreePaymentId } = input;

      if (!cashfreeOrderId || !cashfreePaymentId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cashfree payment details are required",
        });
      }

      // For now, we'll assume verification is successful if payment details are provided
      // In production, you should implement proper Cashfree payment verification
      const isVerified = true;

      const { data: prevBalance, err: walletBalanceFetchErr } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, ctx.session.user.id),
          columns: { walletBalance: true },
        }),
      );

      const walletBalance = prevBalance?.walletBalance;
      if (
        walletBalanceFetchErr ||
        walletBalance === null ||
        walletBalance === undefined
      ) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch wallet balance",
        });
      }

      // For Cashfree, we'll use the transaction amount since we don't have payment fetching implemented
      const amountPaid = Number(transaction.amount); // Amount in rupees

      const totalAmountToBeUpdated = String(Number(walletBalance) + amountPaid);

      const { err: txnError } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          const paymentUpdate = await tx
            .update(kabadiwalaPaymentTransaction)
            .set({
              status: "COMPLETED",
              cashfreePaymentId: cashfreePaymentId,
            })
            .where(eq(kabadiwalaPaymentTransaction.id, transactionId))
            .returning();

          if (!paymentUpdate.length) {
            throw new Error("Failed to update payment transaction");
          }

          const walletUpdate = await tx
            .update(kabadiwala)
            .set({
              walletBalance: totalAmountToBeUpdated,
            })
            .where(eq(kabadiwala.id, ctx.session.user.id))
            .returning();

          if (!walletUpdate.length) {
            throw new Error("Failed to update wallet balance");
          }
        }),
      );

      if (txnError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Payment verification failed: " + txnError.message,
        });
      }

      return {
        success: true,
        message: "Payment verified successfully",
      };
    }),

  viewBill: protectedProcedure
    .input(z.object({ orderId: z.string().nonempty() }))
    .mutation(async ({ ctx, input }) => {
      const { data: orderDetails, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
        }),
      );

      if (
        fetchOrderError ||
        !orderDetails?.securityFeeAmount ||
        !orderDetails.gstPercentage ||
        !orderDetails.gstAmount ||
        !orderDetails.totalServiceChargeWithGst
      ) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      if (orderDetails.kabadiwalaId !== ctx.session.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to view this order",
        });
      }

      if (orderDetails.status !== "COMPLETED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Order is not completed yet",
        });
      }

      // Check if bill already exists
      if (orderDetails.billToKabadiwala) {
        const billData = orderDetails.billToKabadiwala as any;
        if (billData?.bill_url) {
          return {
            billUrl: billData.bill_url,
          };
        }
      }

      // For now, return a placeholder since we're not using Razorpay bills anymore
      // In the future, you could implement bill generation using Cashfree or another service
      throw new TRPCError({
        code: "NOT_IMPLEMENTED",
        message: "Bill generation is not currently available",
      });
    }),
});
