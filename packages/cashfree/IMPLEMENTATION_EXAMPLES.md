# Cashfree Payout Implementation Examples

This document provides practical examples of how to implement Cashfree payout features in the existing codebase.

## 1. Seller Onboarding with Beneficiary Creation

### scraplo-web: Payment Router Update

```typescript
// apps/scraplo-web/src/server/api/router/payment.ts

import {
  Cashfree,
  createBankBeneficiaryRequest,
  validateBeneficiaryRequest,
} from "@acme/cashfree-sdk";

export const paymentRouter = {
  endToEndFundAccountFlow: protectedProcedure
    .input(OnboardingStepThreeSchema)
    .mutation(async ({ ctx, input }) => {
      const { data: userData, err: userErr } = await tryCatch(
        ctx.db.select().from(seller).where(eq(seller.id, ctx.session.user.id)),
      );

      const user = userData?.[0];
      if (userErr || !user) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch user data.",
        });
      }

      // Check if beneficiary already exists
      if (user.cashfreeBeneficiaryId && user.isPaymentVerified) {
        return { success: true };
      }

      // Create beneficiary request using helper function
      const beneficiaryRequest = createBankBeneficiaryRequest({
        beneficiaryId: `seller_${user.id}`,
        name: user.fullName,
        bankAccount: input.accountNumber,
        ifsc: input.ifscCode,
        email: user.email,
        phone: user.phoneNumber,
      });

      // Validate request before sending
      const validationErrors = validateBeneficiaryRequest(beneficiaryRequest);
      if (validationErrors.length > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Validation failed: ${validationErrors.join(", ")}`,
        });
      }

      // Create beneficiary
      const { data: beneficiaryResult, error: beneficiaryError } =
        await cashfree.createBeneficiary(beneficiaryRequest);

      if (beneficiaryError || !beneficiaryResult) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create payment beneficiary.",
        });
      }

      // Update user with beneficiary ID
      const { err: userUpdateErr } = await tryCatch(
        ctx.db
          .update(seller)
          .set({
            cashfreeBeneficiaryId: beneficiaryResult.beneficiary_id,
            isPaymentVerified:
              beneficiaryResult.beneficiary_status === "VERIFIED",
          })
          .where(eq(seller.id, ctx.session.user.id)),
      );

      if (userUpdateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user payment information.",
        });
      }

      return { success: true };
    }),
};
```

## 2. Payout Router Implementation

### scraphub: Enhanced Payout Router

```typescript
// apps/scraphub/src/server/api/router/payout.ts

import {
  Cashfree,
  createBankBeneficiaryRequest,
  createBeneficiaryId,
  validateBeneficiaryRequest,
} from "@acme/cashfree-sdk";

export const payoutRouter = createTRPCRouter({
  // Create contact (beneficiary)
  createContact: protectedProcedure
    .input(CreateContactSchema)
    .mutation(async ({ input }) => {
      try {
        const beneficiaryId = createBeneficiaryId(
          "contact",
          Date.now().toString(),
        );

        const beneficiaryRequest = createBankBeneficiaryRequest({
          beneficiaryId,
          name: input.name,
          bankAccount: input.account_number,
          ifsc: input.ifsc,
          email: input.email,
          phone: input.contact,
        });

        // Validate before creating
        const errors = validateBeneficiaryRequest(beneficiaryRequest);
        if (errors.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Validation failed: ${errors.join(", ")}`,
          });
        }

        const { data, error } =
          await cashfree.createBeneficiary(beneficiaryRequest);

        if (error) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: error.message || "Failed to create beneficiary",
          });
        }

        return {
          id: data?.beneficiary_id,
          name: input.name,
          email: input.email,
          contact: input.contact,
          status: data?.beneficiary_status,
        };
      } catch (error) {
        console.error("Create contact error:", error);
        throw error;
      }
    }),

  // Get beneficiary details
  getBeneficiaryDetails: protectedProcedure
    .input(z.object({ beneficiary_id: z.string() }))
    .query(async ({ input }) => {
      const { data, error } = await cashfree.getBeneficiary({
        beneficiary_id: input.beneficiary_id,
      });

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to get beneficiary details",
        });
      }

      return data;
    }),

  // Check if beneficiary is ready for payouts
  isBeneficiaryReady: protectedProcedure
    .input(z.object({ beneficiary_id: z.string() }))
    .query(async ({ input }) => {
      const { data, error } = await cashfree.getBeneficiary({
        beneficiary_id: input.beneficiary_id,
      });

      if (error || !data) {
        return { ready: false, status: "UNKNOWN" };
      }

      return {
        ready: data.beneficiary_status === "VERIFIED",
        status: data.beneficiary_status,
      };
    }),

  // Remove beneficiary
  removeBeneficiary: protectedProcedure
    .input(z.object({ beneficiary_id: z.string() }))
    .mutation(async ({ input }) => {
      const { data, error } = await cashfree.removeBeneficiary(
        input.beneficiary_id,
      );

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to remove beneficiary",
        });
      }

      return {
        success: true,
        status: data?.beneficiary_status,
      };
    }),
});
```

## 3. Error Handling Best Practices

### Comprehensive Error Handler

```typescript
// Utility function for handling Cashfree errors
export function handleCashfreeError(error: Error): TRPCError {
  const errorMessage = error.message.toLowerCase();

  // Handle specific Cashfree error codes
  if (errorMessage.includes("beneficiary_id_already_exists")) {
    return new TRPCError({
      code: "CONFLICT",
      message: "A beneficiary with this ID already exists",
    });
  }

  if (errorMessage.includes("beneficiary_already_exists")) {
    return new TRPCError({
      code: "CONFLICT",
      message: "A beneficiary with these bank details already exists",
    });
  }

  if (errorMessage.includes("beneficiary_not_found")) {
    return new TRPCError({
      code: "NOT_FOUND",
      message: "Beneficiary not found",
    });
  }

  if (errorMessage.includes("bank_ifsc_invalid")) {
    return new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid IFSC code provided",
    });
  }

  if (errorMessage.includes("bank_account_number_invalid")) {
    return new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid bank account number provided",
    });
  }

  if (errorMessage.includes("apis_not_enabled")) {
    return new TRPCError({
      code: "FORBIDDEN",
      message: "Payout APIs are not enabled for your account",
    });
  }

  // Default error
  return new TRPCError({
    code: "INTERNAL_SERVER_ERROR",
    message: "An error occurred while processing the payout request",
  });
}
```

## 4. Database Integration

### Storing Beneficiary Information

```typescript
// Update seller schema to store Cashfree beneficiary ID
// packages/db/src/schemas/old-schemas.ts

export const seller = pgTable("seller", {
  // ... existing fields
  cashfreeBeneficiaryId: text("cashfreeBeneficiaryId").unique(),
  beneficiaryStatus: text("beneficiaryStatus"), // VERIFIED, INVALID, etc.
  beneficiaryCreatedAt: timestamp("beneficiaryCreatedAt"),
  beneficiaryUpdatedAt: timestamp("beneficiaryUpdatedAt"),
});
```

### Sync Beneficiary Status

```typescript
// Utility to sync beneficiary status from Cashfree
export async function syncBeneficiaryStatus(
  db: Database,
  sellerId: string,
  beneficiaryId: string,
) {
  const { data, error } = await cashfree.getBeneficiary({
    beneficiary_id: beneficiaryId,
  });

  if (error || !data) {
    console.error("Failed to fetch beneficiary status:", error);
    return;
  }

  await db
    .update(seller)
    .set({
      beneficiaryStatus: data.beneficiary_status,
      beneficiaryUpdatedAt: new Date(),
      isPaymentVerified: data.beneficiary_status === "VERIFIED",
    })
    .where(eq(seller.id, sellerId));
}
```

## 5. Frontend Integration

### React Hook for Beneficiary Management

```typescript
// Custom hook for managing beneficiaries
export function useBeneficiary() {
  const createBeneficiary = api.payout.createContact.useMutation();
  const getBeneficiary = api.payout.getBeneficiaryDetails.useQuery;
  const checkStatus = api.payout.isBeneficiaryReady.useQuery;
  const removeBeneficiary = api.payout.removeBeneficiary.useMutation();

  return {
    createBeneficiary,
    getBeneficiary,
    checkStatus,
    removeBeneficiary,
  };
}
```

## 6. Testing

### Unit Tests for Beneficiary Creation

```typescript
// Test file example
describe("Cashfree Beneficiary Management", () => {
  it("should create a valid beneficiary", async () => {
    const beneficiaryRequest = createBankBeneficiaryRequest({
      beneficiaryId: "test_seller_123",
      name: "Test Seller",
      bankAccount: "**********",
      ifsc: "HDFC0000001",
      email: "<EMAIL>",
      phone: "**********",
    });

    const errors = validateBeneficiaryRequest(beneficiaryRequest);
    expect(errors).toHaveLength(0);
  });

  it("should validate IFSC codes correctly", () => {
    expect(validateIFSC("HDFC0000001")).toBe(true);
    expect(validateIFSC("INVALID")).toBe(false);
  });
});
```

## 7. Transfer Implementation (Phase 2)

### Enhanced Payout Router with Transfer APIs

```typescript
// apps/scraphub/src/server/api/router/payout.ts

export const payoutRouter = createTRPCRouter({
  // ... existing beneficiary methods ...

  // Create transfer to existing beneficiary
  createTransfer: protectedProcedure
    .input(
      z.object({
        transferId: z.string(),
        amount: z.number().min(1),
        beneficiaryId: z.string(),
        transferMode: z
          .enum(["banktransfer", "imps", "neft", "rtgs", "upi"])
          .optional(),
        remarks: z.string().max(70).optional(),
      }),
    )
    .mutation(async ({ input }) => {
      try {
        const transferRequest = createSimpleTransferRequest({
          transferId: input.transferId,
          amount: input.amount,
          beneficiaryId: input.beneficiaryId,
          transferMode: input.transferMode || "imps",
          remarks: input.remarks,
        });

        // Validate before creating
        const errors = validateTransferRequest(transferRequest);
        if (errors.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Validation failed: ${errors.join(", ")}`,
          });
        }

        const { data, error } = await cashfree.createTransfer(transferRequest);

        if (error) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: error.message || "Failed to create transfer",
          });
        }

        return {
          transferId: data?.transfer_id,
          cfTransferId: data?.cf_transfer_id,
          status: data?.status,
          amount: data?.transfer_amount,
          utr: data?.transfer_utr,
        };
      } catch (error) {
        console.error("Create transfer error:", error);
        throw error;
      }
    }),

  // Get transfer status
  getTransferStatus: protectedProcedure
    .input(
      z.object({
        transferId: z.string().optional(),
        cfTransferId: z.string().optional(),
      }),
    )
    .query(async ({ input }) => {
      if (!input.transferId && !input.cfTransferId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Either transferId or cfTransferId is required",
        });
      }

      const { data, error } = await cashfree.getTransferStatus({
        transfer_id: input.transferId,
        cf_transfer_id: input.cfTransferId,
      });

      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to get transfer status",
        });
      }

      return data;
    }),

  // Create instant payout (combines beneficiary + transfer)
  createInstantPayout: protectedProcedure
    .input(
      z.object({
        transferId: z.string(),
        amount: z.number().min(1),
        beneficiaryName: z.string(),
        bankAccount: z.string().optional(),
        ifsc: z.string().optional(),
        vpa: z.string().optional(),
        transferMode: z
          .enum(["banktransfer", "imps", "neft", "rtgs", "upi"])
          .optional(),
        remarks: z.string().max(70).optional(),
      }),
    )
    .mutation(async ({ input }) => {
      try {
        // Determine transfer mode based on payment method
        let transferMode = input.transferMode;
        if (!transferMode) {
          transferMode = input.vpa ? "upi" : "imps";
        }

        const transferRequest: CashfreeCreateTransferRequest = {
          transfer_id: input.transferId,
          transfer_amount: input.amount,
          transfer_mode: transferMode,
          beneficiary_details: {
            beneficiary_name: input.beneficiaryName,
            beneficiary_instrument_details: input.vpa
              ? { vpa: input.vpa }
              : {
                  bank_account_number: input.bankAccount!,
                  bank_ifsc: input.ifsc!,
                },
          },
          transfer_remarks: input.remarks,
        };

        // Validate before creating
        const errors = validateTransferRequest(transferRequest);
        if (errors.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Validation failed: ${errors.join(", ")}`,
          });
        }

        const { data, error } = await cashfree.createTransfer(transferRequest);

        if (error) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: error.message || "Failed to create instant payout",
          });
        }

        return {
          transferId: data?.transfer_id,
          cfTransferId: data?.cf_transfer_id,
          status: data?.status,
          amount: data?.transfer_amount,
          mode: data?.transfer_mode,
        };
      } catch (error) {
        console.error("Create instant payout error:", error);
        throw error;
      }
    }),
});
```

### 8. Seller Payout Implementation

```typescript
// apps/scraplo-web/src/server/api/router/seller.ts

export const sellerRouter = createTRPCRouter({
  // Request payout for seller
  requestPayout: protectedProcedure
    .input(
      z.object({
        amount: z.number().min(1),
        remarks: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const sellerId = ctx.session.user.id;

      // Get seller details
      const seller = await ctx.db.query.seller.findFirst({
        where: eq(sellerTable.id, sellerId),
      });

      if (!seller?.cashfreeBeneficiaryId) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Payment details not set up. Please complete your profile.",
        });
      }

      if (!seller.isPaymentVerified) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Payment details not verified. Please contact support.",
        });
      }

      // Generate unique transfer ID
      const transferId = createTransferId(
        "seller_payout",
        `${sellerId}_${Date.now()}`,
      );

      try {
        const transferRequest = createSimpleTransferRequest({
          transferId,
          amount: input.amount,
          beneficiaryId: seller.cashfreeBeneficiaryId,
          transferMode: "imps",
          remarks: input.remarks || `Payout for seller ${seller.fullName}`,
        });

        const { data, error } = await cashfree.createTransfer(transferRequest);

        if (error) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to initiate payout. Please try again.",
          });
        }

        // Store payout record in database
        await ctx.db.insert(payoutTable).values({
          id: transferId,
          sellerId,
          amount: input.amount,
          status: data?.status || "RECEIVED",
          cfTransferId: data?.cf_transfer_id,
          transferMode: "imps",
          remarks: input.remarks,
          createdAt: new Date(),
        });

        return {
          transferId: data?.transfer_id,
          status: data?.status,
          amount: data?.transfer_amount,
        };
      } catch (error) {
        console.error("Seller payout error:", error);
        throw error;
      }
    }),

  // Get payout history
  getPayoutHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      const sellerId = ctx.session.user.id;

      const payouts = await ctx.db.query.payout.findMany({
        where: eq(payoutTable.sellerId, sellerId),
        limit: input.limit,
        offset: input.offset,
        orderBy: desc(payoutTable.createdAt),
      });

      return payouts;
    }),

  // Get specific payout status
  getPayoutStatus: protectedProcedure
    .input(z.object({ transferId: z.string() }))
    .query(async ({ ctx, input }) => {
      const sellerId = ctx.session.user.id;

      // Verify payout belongs to seller
      const payout = await ctx.db.query.payout.findFirst({
        where: and(
          eq(payoutTable.id, input.transferId),
          eq(payoutTable.sellerId, sellerId),
        ),
      });

      if (!payout) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Payout not found",
        });
      }

      // Get latest status from Cashfree
      const { data, error } = await cashfree.getTransferStatus({
        transfer_id: input.transferId,
      });

      if (error) {
        // Return database status if API call fails
        return {
          transferId: payout.id,
          status: payout.status,
          amount: payout.amount,
          updatedAt: payout.updatedAt,
        };
      }

      // Update database with latest status
      if (data && data.status !== payout.status) {
        await ctx.db
          .update(payoutTable)
          .set({
            status: data.status,
            transferUtr: data.transfer_utr,
            updatedAt: new Date(),
          })
          .where(eq(payoutTable.id, input.transferId));
      }

      return {
        transferId: data?.transfer_id,
        cfTransferId: data?.cf_transfer_id,
        status: data?.status,
        amount: data?.transfer_amount,
        utr: data?.transfer_utr,
        serviceCharge: data?.transfer_service_charge,
        serviceTax: data?.transfer_service_tax,
        addedOn: data?.added_on,
        updatedOn: data?.updated_on,
      };
    }),
});
```

### 9. Frontend Integration

```typescript
// Custom hook for transfer management
export function useTransfers() {
  const createTransfer = api.payout.createTransfer.useMutation();
  const getTransferStatus = api.payout.getTransferStatus.useQuery;
  const createInstantPayout = api.payout.createInstantPayout.useMutation();

  return {
    createTransfer,
    getTransferStatus,
    createInstantPayout,
  };
}

// Seller payout hook
export function useSellerPayouts() {
  const requestPayout = api.seller.requestPayout.useMutation();
  const getPayoutHistory = api.seller.getPayoutHistory.useQuery;
  const getPayoutStatus = api.seller.getPayoutStatus.useQuery;

  return {
    requestPayout,
    getPayoutHistory,
    getPayoutStatus,
  };
}
```

### 10. Database Schema Updates

```typescript
// Add payout tracking table
export const payout = pgTable("payout", {
  id: text("id").primaryKey(), // transfer_id
  sellerId: text("sellerId")
    .notNull()
    .references(() => seller.id),
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  status: text("status").notNull(), // RECEIVED, PENDING, SUCCESS, etc.
  cfTransferId: text("cfTransferId"),
  transferMode: text("transferMode").notNull(),
  transferUtr: text("transferUtr"),
  serviceCharge: decimal("serviceCharge", { precision: 10, scale: 2 }),
  serviceTax: decimal("serviceTax", { precision: 10, scale: 2 }),
  remarks: text("remarks"),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").defaultNow().notNull(),
});
```

## Next Steps

Phase 3 will include:

1. **Webhook Integration**: Real-time status updates
2. **Bulk Operations**: Batch transfers and beneficiary operations
3. **Advanced Features**: Transfer scheduling, recurring payouts
4. **Monitoring & Analytics**: Comprehensive payout tracking and reporting
5. **Error Recovery**: Automatic retry mechanisms and failure handling

This completes the Phase 2 implementation of Cashfree payout features with full transfer management capabilities.
