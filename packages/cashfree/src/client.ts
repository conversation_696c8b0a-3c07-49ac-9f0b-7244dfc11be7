import * as crypto from "crypto";
import axios, { AxiosInstance, AxiosRequestConfig, Method } from "axios";

import { CashfreeConfig } from "./types";

export class CashfreeClient {
  private axiosInstance: AxiosInstance;
  private baseURL: string;
  private appId: string;
  private secretKey: string;

  constructor(config: CashfreeConfig) {
    this.appId = config.appId;
    this.secretKey = config.secretKey;

    // Set base URL based on environment
    this.baseURL =
      config.baseURL ||
      (config.environment === "production"
        ? "https://api.cashfree.com"
        : "https://sandbox.cashfree.com");

    // Create axios instance with auth headers
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      headers: {
        "Content-Type": "application/json",
        "x-client-id": this.appId,
        "x-client-secret": this.secretKey,
        "x-api-version": "2024-01-01",
      },
    });

    // Add request interceptor for logging
    this.axiosInstance.interceptors.request.use(
      (config) => {
        console.log(
          `Cashfree API Request: ${config.method?.toUpperCase()} ${config.url}`,
        );
        return config;
      },
      (error) => {
        console.error("Cashfree API Request Error:", error);
        return Promise.reject(error);
      },
    );

    // Add response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => {
        console.log(
          `Cashfree API Response: ${response.status} ${response.config.url}`,
        );
        return response;
      },
      (error) => {
        console.error(
          "Cashfree API Response Error:",
          error.response?.data || error.message,
        );
        return Promise.reject(error);
      },
    );
  }

  /**
   * Make a request to the Cashfree API
   * @param method HTTP method
   * @param endpoint API endpoint
   * @param data Request data (for POST, PUT, PATCH)
   * @param config Additional axios config
   * @returns Promise with response data
   */
  async request<T>(
    method: Method,
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    try {
      const response = await this.axiosInstance({
        method,
        url: endpoint,
        data,
        ...config,
      });

      return response.data;
    } catch (error: any) {
      // Handle Cashfree specific errors
      if (error.response?.data) {
        const cashfreeError = error.response.data;
        throw new Error(
          `Cashfree API Error: ${cashfreeError.message || cashfreeError.error_description || error.message}`,
        );
      }
      throw error;
    }
  }

  /**
   * Generate signature for webhook verification
   * @param rawBody Raw webhook body
   * @param timestamp Timestamp from webhook headers
   * @returns Generated signature
   */
  generateWebhookSignature(rawBody: string, timestamp: string): string {
    const signatureData = timestamp + rawBody;
    return crypto
      .createHmac("sha256", this.secretKey)
      .update(signatureData)
      .digest("base64");
  }

  /**
   * Verify webhook signature
   * @param rawBody Raw webhook body
   * @param timestamp Timestamp from webhook headers
   * @param signature Signature from webhook headers
   * @returns Boolean indicating if signature is valid
   */
  verifyWebhookSignature(
    rawBody: string,
    timestamp: string,
    signature: string,
  ): boolean {
    const expectedSignature = this.generateWebhookSignature(rawBody, timestamp);
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature),
    );
  }

  /**
   * Generate payment signature for order verification
   * @param orderId Order ID
   * @param orderAmount Order amount
   * @param orderCurrency Order currency
   * @param customerEmail Customer email
   * @param customerName Customer name
   * @param customerPhone Customer phone
   * @returns Generated signature
   */
  generatePaymentSignature(
    orderId: string,
    orderAmount: number,
    orderCurrency: string,
    customerEmail?: string,
    customerName?: string,
    customerPhone?: string,
  ): string {
    const signatureData = [
      orderId,
      orderAmount.toString(),
      orderCurrency,
      customerEmail || "",
      customerName || "",
      customerPhone || "",
    ].join("|");

    return crypto
      .createHmac("sha256", this.secretKey)
      .update(signatureData)
      .digest("base64");
  }

  /**
   * Verify payment signature
   * @param orderId Order ID
   * @param orderAmount Order amount
   * @param orderCurrency Order currency
   * @param signature Signature to verify
   * @param customerEmail Customer email
   * @param customerName Customer name
   * @param customerPhone Customer phone
   * @returns Boolean indicating if signature is valid
   */
  verifyPaymentSignature(
    orderId: string,
    orderAmount: number,
    orderCurrency: string,
    signature: string,
    customerEmail?: string,
    customerName?: string,
    customerPhone?: string,
  ): boolean {
    const expectedSignature = this.generatePaymentSignature(
      orderId,
      orderAmount,
      orderCurrency,
      customerEmail,
      customerName,
      customerPhone,
    );

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature),
    );
  }
}
