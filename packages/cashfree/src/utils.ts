// Utility functions for Cashfree Payout API

import {
  CashfreeCreateBeneficiaryRequest,
  CashfreeCreateTransferRequest,
  CashfreeTransferMode,
} from "./types";

/**
 * Validates a beneficiary ID according to Cashfree requirements
 * - Max 50 characters
 * - Only alphanumeric, underscore (_), pipe (|), and dot (.) allowed
 */
export function validateBeneficiaryId(beneficiaryId: string): boolean {
  if (!beneficiaryId || beneficiaryId.length > 50) {
    return false;
  }

  const validPattern = /^[a-zA-Z0-9._|]+$/;
  return validPattern.test(beneficiaryId);
}

/**
 * Validates an IFSC code according to Indian banking standards
 * - Exactly 11 characters
 * - First 4 characters: alphabets
 * - 5th character: 0
 * - Last 6 characters: alphanumeric
 */
export function validateIFSC(ifsc: string): boolean {
  if (!ifsc || ifsc.length !== 11) {
    return false;
  }

  const ifscPattern = /^[A-Z]{4}0[A-Z0-9]{6}$/;
  return ifscPattern.test(ifsc.toUpperCase());
}

/**
 * Validates a bank account number
 * - Between 4 and 25 characters
 * - Only alphanumeric characters allowed
 */
export function validateBankAccount(accountNumber: string): boolean {
  if (!accountNumber || accountNumber.length < 4 || accountNumber.length > 25) {
    return false;
  }

  const accountPattern = /^[a-zA-Z0-9]+$/;
  return accountPattern.test(accountNumber);
}

/**
 * Validates a UPI VPA (Virtual Payment Address)
 * - Format: username@bankname
 * - Alphanumeric with period (.), hyphen (-), underscore (_), and @ allowed
 * - Hyphen (-) only allowed before @
 */
export function validateUPI(vpa: string): boolean {
  if (!vpa) {
    return false;
  }

  const upiPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+$/;
  return upiPattern.test(vpa);
}

/**
 * Validates an Indian phone number
 * - Between 8 and 12 digits after removing +91
 * - Only digits allowed
 */
export function validatePhoneNumber(phone: string): boolean {
  if (!phone) {
    return false;
  }

  // Remove +91 if present
  const cleanPhone = phone.replace(/^\+91/, "");

  if (cleanPhone.length < 8 || cleanPhone.length > 12) {
    return false;
  }

  const phonePattern = /^[0-9]+$/;
  return phonePattern.test(cleanPhone);
}

/**
 * Validates an email address
 * - Max 200 characters
 * - Must contain @ and .
 */
export function validateEmail(email: string): boolean {
  if (!email || email.length > 200) {
    return false;
  }

  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
}

/**
 * Validates a postal code (PIN code)
 * - Exactly 6 digits
 */
export function validatePostalCode(postalCode: string): boolean {
  if (!postalCode || postalCode.length !== 6) {
    return false;
  }

  const postalPattern = /^[0-9]{6}$/;
  return postalPattern.test(postalCode);
}

/**
 * Validates a complete beneficiary request
 * Returns an array of validation errors, empty if valid
 */
export function validateBeneficiaryRequest(
  request: CashfreeCreateBeneficiaryRequest,
): string[] {
  const errors: string[] = [];

  // Required fields
  if (!request.beneficiary_id) {
    errors.push("beneficiary_id is required");
  } else if (!validateBeneficiaryId(request.beneficiary_id)) {
    errors.push(
      "beneficiary_id must be max 50 characters and contain only alphanumeric, underscore, pipe, and dot characters",
    );
  }

  if (!request.beneficiary_name) {
    errors.push("beneficiary_name is required");
  } else if (request.beneficiary_name.length > 100) {
    errors.push("beneficiary_name must be max 100 characters");
  }

  // Instrument details validation
  const instruments = request.beneficiary_instrument_details;
  if (instruments) {
    // Bank account validation
    if (instruments.bank_account_number && !instruments.bank_ifsc) {
      errors.push("bank_ifsc is required when bank_account_number is provided");
    }
    if (instruments.bank_ifsc && !instruments.bank_account_number) {
      errors.push("bank_account_number is required when bank_ifsc is provided");
    }

    if (
      instruments.bank_account_number &&
      !validateBankAccount(instruments.bank_account_number)
    ) {
      errors.push("bank_account_number must be 4-25 alphanumeric characters");
    }

    if (instruments.bank_ifsc && !validateIFSC(instruments.bank_ifsc)) {
      errors.push("bank_ifsc must be a valid 11-character IFSC code");
    }

    // UPI validation
    if (instruments.vpa && !validateUPI(instruments.vpa)) {
      errors.push("vpa must be a valid UPI address");
    }
  }

  // Contact details validation
  const contact = request.beneficiary_contact_details;
  if (contact) {
    if (
      contact.beneficiary_email &&
      !validateEmail(contact.beneficiary_email)
    ) {
      errors.push(
        "beneficiary_email must be a valid email address (max 200 characters)",
      );
    }

    if (
      contact.beneficiary_phone &&
      !validatePhoneNumber(contact.beneficiary_phone)
    ) {
      errors.push("beneficiary_phone must be 8-12 digits");
    }

    if (
      contact.beneficiary_postal_code &&
      !validatePostalCode(contact.beneficiary_postal_code)
    ) {
      errors.push("beneficiary_postal_code must be exactly 6 digits");
    }
  }

  return errors;
}

/**
 * Creates a beneficiary ID from a user ID with a prefix
 * Ensures the ID is valid and unique
 */
export function createBeneficiaryId(prefix: string, userId: string): string {
  const beneficiaryId = `${prefix}_${userId}`;

  if (!validateBeneficiaryId(beneficiaryId)) {
    throw new Error(`Generated beneficiary ID "${beneficiaryId}" is invalid`);
  }

  return beneficiaryId;
}

/**
 * Formats a phone number for Cashfree API
 * Removes +91 country code if present
 */
export function formatPhoneNumber(phone: string): string {
  return phone.replace(/^\+91/, "");
}

/**
 * Formats an IFSC code to uppercase
 */
export function formatIFSC(ifsc: string): string {
  return ifsc.toUpperCase();
}

/**
 * Helper to create a bank account beneficiary request
 */
export function createBankBeneficiaryRequest(params: {
  beneficiaryId: string;
  name: string;
  bankAccount: string;
  ifsc: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
}): CashfreeCreateBeneficiaryRequest {
  return {
    beneficiary_id: params.beneficiaryId,
    beneficiary_name: params.name,
    beneficiary_instrument_details: {
      bank_account_number: params.bankAccount,
      bank_ifsc: formatIFSC(params.ifsc),
    },
    beneficiary_contact_details: {
      beneficiary_email: params.email,
      beneficiary_phone: params.phone
        ? formatPhoneNumber(params.phone)
        : undefined,
      beneficiary_country_code: params.phone ? "+91" : undefined,
      beneficiary_address: params.address,
      beneficiary_city: params.city,
      beneficiary_state: params.state,
      beneficiary_postal_code: params.postalCode,
    },
  };
}

/**
 * Helper to create a UPI beneficiary request
 */
export function createUPIBeneficiaryRequest(params: {
  beneficiaryId: string;
  name: string;
  vpa: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
}): CashfreeCreateBeneficiaryRequest {
  return {
    beneficiary_id: params.beneficiaryId,
    beneficiary_name: params.name,
    beneficiary_instrument_details: {
      vpa: params.vpa,
    },
    beneficiary_contact_details: {
      beneficiary_email: params.email,
      beneficiary_phone: params.phone
        ? formatPhoneNumber(params.phone)
        : undefined,
      beneficiary_country_code: params.phone ? "+91" : undefined,
      beneficiary_address: params.address,
      beneficiary_city: params.city,
      beneficiary_state: params.state,
      beneficiary_postal_code: params.postalCode,
    },
  };
}

// ============ TRANSFER UTILITY FUNCTIONS ============

/**
 * Validates a transfer ID according to Cashfree requirements
 * - Max 40 characters
 * - Only alphanumeric and underscore allowed
 */
export function validateTransferId(transferId: string): boolean {
  if (!transferId || transferId.length > 40) {
    return false;
  }

  const validPattern = /^[a-zA-Z0-9_]+$/;
  return validPattern.test(transferId);
}

/**
 * Validates transfer amount
 * - Must be >= 1.00
 */
export function validateTransferAmount(amount: number): boolean {
  return amount >= 1.0;
}

/**
 * Validates transfer remarks
 * - Max 70 characters
 * - Alphanumeric and whitespaces allowed
 */
export function validateTransferRemarks(remarks: string): boolean {
  if (!remarks) return true; // Optional field

  if (remarks.length > 70) {
    return false;
  }

  const validPattern = /^[a-zA-Z0-9\s]+$/;
  return validPattern.test(remarks);
}

/**
 * Validates a complete transfer request
 * Returns an array of validation errors, empty if valid
 */
export function validateTransferRequest(
  request: CashfreeCreateTransferRequest,
): string[] {
  const errors: string[] = [];

  // Required fields
  if (!request.transfer_id) {
    errors.push("transfer_id is required");
  } else if (!validateTransferId(request.transfer_id)) {
    errors.push(
      "transfer_id must be max 40 characters and contain only alphanumeric and underscore characters",
    );
  }

  if (!request.transfer_amount) {
    errors.push("transfer_amount is required");
  } else if (!validateTransferAmount(request.transfer_amount)) {
    errors.push("transfer_amount must be >= 1.00");
  }

  if (!request.beneficiary_details) {
    errors.push("beneficiary_details is required");
  } else {
    // Validate beneficiary details
    const beneficiary = request.beneficiary_details;

    if (!beneficiary.beneficiary_id && !beneficiary.beneficiary_name) {
      errors.push(
        "Either beneficiary_id or beneficiary_name is required in beneficiary_details",
      );
    }

    // If providing instrument details, validate them
    if (beneficiary.beneficiary_instrument_details) {
      const instruments = beneficiary.beneficiary_instrument_details;

      // Bank account validation
      if (instruments.bank_account_number && !instruments.bank_ifsc) {
        errors.push(
          "bank_ifsc is required when bank_account_number is provided",
        );
      }
      if (instruments.bank_ifsc && !instruments.bank_account_number) {
        errors.push(
          "bank_account_number is required when bank_ifsc is provided",
        );
      }

      if (
        instruments.bank_account_number &&
        !validateBankAccount(instruments.bank_account_number)
      ) {
        errors.push("bank_account_number must be 4-25 alphanumeric characters");
      }

      if (instruments.bank_ifsc && !validateIFSC(instruments.bank_ifsc)) {
        errors.push("bank_ifsc must be a valid 11-character IFSC code");
      }

      // UPI validation
      if (instruments.vpa && !validateUPI(instruments.vpa)) {
        errors.push("vpa must be a valid UPI address");
      }
    }
  }

  // Optional field validations
  if (
    request.transfer_remarks &&
    !validateTransferRemarks(request.transfer_remarks)
  ) {
    errors.push(
      "transfer_remarks must be max 70 characters with alphanumeric and whitespace characters only",
    );
  }

  return errors;
}

/**
 * Creates a transfer ID with a prefix and unique identifier
 */
export function createTransferId(prefix: string, uniqueId: string): string {
  const transferId = `${prefix}_${uniqueId}`;

  if (!validateTransferId(transferId)) {
    throw new Error(`Generated transfer ID "${transferId}" is invalid`);
  }

  return transferId;
}

/**
 * Helper to create a simple transfer request to existing beneficiary
 */
export function createSimpleTransferRequest(params: {
  transferId: string;
  amount: number;
  beneficiaryId: string;
  transferMode?: CashfreeTransferMode;
  remarks?: string;
  fundsourceId?: string;
}): CashfreeCreateTransferRequest {
  return {
    transfer_id: params.transferId,
    transfer_amount: params.amount,
    transfer_currency: "INR",
    transfer_mode: params.transferMode || "banktransfer",
    beneficiary_details: {
      beneficiary_id: params.beneficiaryId,
    },
    transfer_remarks: params.remarks,
    fundsource_id: params.fundsourceId,
  };
}
