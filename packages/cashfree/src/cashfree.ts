import { AxiosError } from "axios";
import {
  Cashfree as CashfreePG,
  CFEnvironment,
  CustomerEntity,
  OrderEntity,
  PGWebhookEvent,
} from "cashfree-pg";

import { CashfreeClient } from "./client";
import {
  CashfreeBeneficiaryResponse,
  CashfreeConfig,
  CashfreeCreateBeneficiaryRequest,
  CashfreeCreateCustomerRequest,
  CashfreeCreateOrderRequest,
  CashfreeCreateTransferRequest,
  CashfreeGetBeneficiaryRequest,
  CashfreeGetTransferStatusRequest,
  CashfreePaymentDetails,
  CashfreeResponse,
  CashfreeSettlementResponse,
  CashfreeTransferResponse,
} from "./types";

export class Cashfree {
  private client: CashfreeClient;
  private cashfreePG: CashfreePG;

  constructor(config: CashfreeConfig) {
    this.client = new CashfreeClient(config);

    // Initialize Cashfree PG SDK for backend operations
    // CashfreePG.XClientId = config.appId;
    // CashfreePG.XClientSecret = config.secretKey;
    const environment =
      config.environment === "production"
        ? CFEnvironment.PRODUCTION
        : CFEnvironment.SANDBOX;

    this.cashfreePG = new CashfreePG(
      environment,
      config.appId,
      config.secretKey,
    );
  }

  // Customer API
  async createCustomer(
    customer: CashfreeCreateCustomerRequest,
  ): Promise<CashfreeResponse<CustomerEntity>> {
    try {
      // Use official Cashfree PG SDK for customer creation
      const request = {
        // customer_id: customer.customer_id,
        customer_name: customer.customer_name || "",
        customer_email: customer.customer_email || "",
        customer_phone: customer.customer_phone || "",
      };

      const response = await this.cashfreePG.PGCreateCustomer(request);

      return {
        data: response.data,
        isLoading: false,
        error: null,
      };
    } catch (error) {
      console.error("Cashfree createCustomer error", JSON.stringify(error));
      return {
        data: null,
        isLoading: false,
        error: error as Error,
      };
    }
  }

  // Orders API
  async createOrder(order: CashfreeCreateOrderRequest): Promise<{
    data: OrderEntity | null;
    isLoading: boolean;
    error: Error | null;
  }> {
    try {
      // Use official Cashfree PG SDK for order creation
      const request = {
        order_id: order.order_id,
        order_amount: order.order_amount,
        order_currency: order.order_currency,
        customer_details: {
          customer_id: order.customer_details.customer_id,
          customer_name: order.customer_details.customer_name,
          customer_email: order.customer_details.customer_email,
          customer_phone: order.customer_details.customer_phone,
        },
        order_meta: order.order_meta,
        order_note: order.order_note,
      };

      console.log("Cashfree createOrder request", JSON.stringify(request));

      const response = await this.cashfreePG.PGCreateOrder(request);

      if (response.status !== 200 || !response.data) {
        throw new Error("Failed to create order");
      }

      console.log(
        "Cashfree createOrder response",
        JSON.stringify(response.data),
      );

      return {
        data: response.data,
        isLoading: false,
        error: null,
      };
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        console.error(
          "Cashfree createOrder error",
          JSON.stringify(error.response?.data),
        );
      } else {
        console.error("Cashfree createOrder error", JSON.stringify(error));
      }
      return {
        data: null,
        isLoading: false,
        error: error as Error,
      };
    }
  }

  async getOrder(orderId: string): Promise<{
    data: OrderEntity | null;
    isLoading: boolean;
    error: Error | null;
  }> {
    try {
      const data = await this.cashfreePG.PGFetchOrder(orderId);

      if (data.status !== 200 || !data.data) {
        throw new Error("Failed to fetch order");
      }

      return { data: data.data, isLoading: false, error: null };
      // const data = await this.client.request<CashfreeOrderResponse>(
      //   "GET",
      //   `/pg/orders/${orderId}`,
      // );
      // return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Payments API
  async getPaymentDetails(
    orderId: string,
    cfPaymentId?: string,
  ): Promise<CashfreeResponse<CashfreePaymentDetails[]>> {
    try {
      const endpoint = cfPaymentId
        ? `/pg/orders/${orderId}/payments/${cfPaymentId}`
        : `/pg/orders/${orderId}/payments`;

      const data = await this.client.request<CashfreePaymentDetails[]>(
        "GET",
        endpoint,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Beneficiaries API (equivalent to contacts) - Updated to match Cashfree API V2
  async createBeneficiary(
    beneficiary: CashfreeCreateBeneficiaryRequest,
  ): Promise<CashfreeResponse<CashfreeBeneficiaryResponse>> {
    try {
      const response = await this.client.request<CashfreeBeneficiaryResponse>(
        "POST",
        "/payout/beneficiary",
        beneficiary,
      );

      return {
        data: response,
        isLoading: false,
        error: null,
      };
    } catch (error) {
      console.error("Cashfree createBeneficiary error:", error);
      return {
        data: null,
        isLoading: false,
        error: error as Error,
      };
    }
  }

  async getBeneficiary(
    params: CashfreeGetBeneficiaryRequest,
  ): Promise<CashfreeResponse<CashfreeBeneficiaryResponse>> {
    try {
      const queryParams = new URLSearchParams();

      if (params.beneficiary_id) {
        queryParams.append("beneficiary_id", params.beneficiary_id);
      } else if (params.bank_account_number && params.bank_ifsc) {
        queryParams.append("bank_account_number", params.bank_account_number);
        queryParams.append("bank_ifsc", params.bank_ifsc);
      } else {
        throw new Error(
          "Either beneficiary_id or both bank_account_number and bank_ifsc are required",
        );
      }

      const data = await this.client.request<CashfreeBeneficiaryResponse>(
        "GET",
        `/payout/beneficiary?${queryParams.toString()}`,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      console.error("Cashfree getBeneficiary error:", error);
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async removeBeneficiary(
    beneficiaryId: string,
  ): Promise<CashfreeResponse<CashfreeBeneficiaryResponse>> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append("beneficiary_id", beneficiaryId);

      const data = await this.client.request<CashfreeBeneficiaryResponse>(
        "DELETE",
        `/payout/beneficiary?${queryParams.toString()}`,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      console.error("Cashfree removeBeneficiary error:", error);
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async createPayout(
    transferData: CashfreeCreateTransferRequest,
  ): Promise<CashfreeResponse<CashfreeTransferResponse>> {
    return this.createTransfer(transferData);
  }

  async getPayoutStatus(
    transferId: string,
  ): Promise<CashfreeResponse<CashfreeTransferResponse>> {
    return this.getTransferStatus(transferId);
  }

  // Transfers API V2 - Phase 2 Implementation
  async createTransfer(
    transfer: CashfreeCreateTransferRequest,
  ): Promise<CashfreeResponse<CashfreeTransferResponse>> {
    try {
      const response = await this.client.request<CashfreeTransferResponse>(
        "POST",
        "/payout/transfers",
        transfer,
      );

      return {
        data: response,
        isLoading: false,
        error: null,
      };
    } catch (error) {
      console.error("Cashfree createTransfer error:", error);
      return {
        data: null,
        isLoading: false,
        error: error as Error,
      };
    }
  }

  async getTransferStatus(
    params: CashfreeGetTransferStatusRequest | string,
  ): Promise<CashfreeResponse<CashfreeTransferResponse>> {
    try {
      let queryParams: URLSearchParams;

      if (typeof params === "string") {
        // Backward compatibility - treat as transfer_id
        queryParams = new URLSearchParams();
        queryParams.append("transfer_id", params);
      } else {
        queryParams = new URLSearchParams();
        if (params.transfer_id) {
          queryParams.append("transfer_id", params.transfer_id);
        } else if (params.cf_transfer_id) {
          queryParams.append("cf_transfer_id", params.cf_transfer_id);
        } else {
          throw new Error("Either transfer_id or cf_transfer_id is required");
        }
      }

      const data = await this.client.request<CashfreeTransferResponse>(
        "GET",
        `/payout/transfers?${queryParams.toString()}`,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      console.error("Cashfree getTransferStatus error:", error);
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Settlements API
  async getSettlements(
    maxReturn?: number,
    lastId?: string,
  ): Promise<CashfreeResponse<CashfreeSettlementResponse[]>> {
    try {
      const params = new URLSearchParams();
      if (maxReturn) params.append("maxReturn", maxReturn.toString());
      if (lastId) params.append("lastId", lastId);

      const data = await this.client.request<CashfreeSettlementResponse[]>(
        "GET",
        `/pg/settlements?${params.toString()}`,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Verification methods
  verifyWebhookSignature(
    rawBody: string,
    timestamp: string,
    signature: string,
  ): PGWebhookEvent {
    return this.cashfreePG.PGVerifyWebhookSignature(
      signature,
      rawBody,
      timestamp,
    );
  }

  verifyPaymentSignature(
    orderId: string,
    orderAmount: number,
    orderCurrency: string,
    signature: string,
    customerEmail?: string,
    customerName?: string,
    customerPhone?: string,
  ): boolean {
    return this.client.verifyPaymentSignature(
      orderId,
      orderAmount,
      orderCurrency,
      signature,
      customerEmail,
      customerName,
      customerPhone,
    );
  }

  // Utility methods
  generatePaymentSignature(
    orderId: string,
    orderAmount: number,
    orderCurrency: string,
    customerEmail?: string,
    customerName?: string,
    customerPhone?: string,
  ): string {
    return this.client.generatePaymentSignature(
      orderId,
      orderAmount,
      orderCurrency,
      customerEmail,
      customerName,
      customerPhone,
    );
  }
}
