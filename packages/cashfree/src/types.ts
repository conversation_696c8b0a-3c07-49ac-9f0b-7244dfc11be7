// Cashfree specific types and interfaces

export interface CashfreeConfig {
  appId: string;
  secretKey: string;
  environment: "sandbox" | "production";
  baseURL?: string;
}

export interface CashfreeResponse<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
}

// Customer interfaces
export interface CashfreeCreateCustomerRequest {
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
}

export interface CashfreeCustomerResponse {
  customer_id: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  customer_bank_account_number?: string;
  customer_bank_ifsc?: string;
  customer_bank_code?: string;
  created_at?: string;
  updated_at?: string;
}

// Order interfaces
export interface CashfreeCreateOrderRequest {
  order_id: string;
  order_amount: number;
  order_currency: string;
  customer_details: {
    customer_id: string;
    customer_name: string;
    customer_email: string;
    customer_phone: string;
  };
  order_meta?: {
    return_url?: string;
    notify_url?: string;
    payment_methods?: string;
  };
  order_note?: string;
  order_tags?: Record<string, string>;
}

export interface CashfreeOrderResponse {
  cf_order_id: string;
  order_id: string;
  entity: string;
  order_currency: string;
  order_amount: number;
  order_expiry_time: string;
  customer_details: {
    customer_id: string;
    customer_name?: string;
    customer_email?: string;
    customer_phone?: string;
  };
  order_meta?: {
    return_url?: string;
    notify_url?: string;
    payment_methods?: string;
  };
  settlements: {
    url: string;
  };
  payments: {
    url: string;
  };
  refunds: {
    url: string;
  };
  order_status: string;
  order_token: string;
  order_note?: string;
  order_tags?: Record<string, string>;
  created_at: string;
}

// Payment interfaces
export interface CashfreePaymentDetails {
  cf_payment_id: string;
  order_id: string;
  entity: string;
  payment_currency: string;
  payment_amount: number;
  payment_time: string;
  payment_completion_time: string;
  payment_status: string;
  payment_message: string;
  bank_reference: string;
  auth_id: string;
  payment_method: {
    card?: {
      channel: string;
      card_number: string;
      card_network: string;
      card_type: string;
      card_country: string;
      card_bank_name: string;
    };
    netbanking?: {
      channel: string;
      netbanking_bank_code: string;
      netbanking_bank_name: string;
    };
    upi?: {
      channel: string;
      upi_id: string;
    };
    wallet?: {
      channel: string;
      wallet_name: string;
    };
  };
}

// Beneficiary interfaces (equivalent to contacts) - Updated to match Cashfree API V2
export interface CashfreeCreateBeneficiaryRequest {
  beneficiary_id: string;
  beneficiary_name: string;
  beneficiary_instrument_details?: {
    bank_account_number?: string;
    bank_ifsc?: string;
    vpa?: string;
  };
  beneficiary_contact_details?: {
    beneficiary_email?: string;
    beneficiary_phone?: string;
    beneficiary_country_code?: string;
    beneficiary_address?: string;
    beneficiary_city?: string;
    beneficiary_state?: string;
    beneficiary_postal_code?: string;
  };
}

export interface CashfreeBeneficiaryResponse {
  beneficiary_id: string;
  beneficiary_name: string;
  beneficiary_instrument_details?: {
    bank_account_number?: string;
    bank_ifsc?: string;
    vpa?: string;
  };
  beneficiary_contact_details?: {
    beneficiary_email?: string;
    beneficiary_phone?: string;
    beneficiary_country_code?: string;
    beneficiary_address?: string;
    beneficiary_city?: string;
    beneficiary_state?: string;
    beneficiary_postal_code?: string;
  };
  beneficiary_status:
    | "VERIFIED"
    | "INVALID"
    | "INITIATED"
    | "CANCELLED"
    | "FAILED"
    | "DELETED";
  added_on: string;
}

// Get Beneficiary Request interface
export interface CashfreeGetBeneficiaryRequest {
  beneficiary_id?: string;
  bank_account_number?: string;
  bank_ifsc?: string;
}

// Transfer interfaces - Phase 2 Implementation
export type CashfreeTransferMode =
  | "banktransfer"
  | "imps"
  | "neft"
  | "rtgs"
  | "upi"
  | "paytm"
  | "amazonpay"
  | "card"
  | "cardupi";

export type CashfreeTransferStatus =
  | "RECEIVED"
  | "APPROVAL_PENDING"
  | "PENDING"
  | "SUCCESS"
  | "FAILED"
  | "REJECTED"
  | "REVERSED";

export interface CashfreeCardDetails {
  card_token: string;
  card_network_type: "VISA" | "MASTERCARD";
  card_cryptogram?: string;
  card_token_expiry?: string;
  card_type?: "DEBIT" | "CREDIT";
  card_token_PAN_sequence_number?: string;
}

export interface CashfreeBeneficiaryInstrumentDetails {
  bank_account_number?: string;
  bank_ifsc?: string;
  vpa?: string;
  card_details?: CashfreeCardDetails;
}

export interface CashfreeBeneficiaryContactDetails {
  beneficiary_email?: string;
  beneficiary_phone?: string;
  beneficiary_country_code?: string;
  beneficiary_address?: string;
  beneficiary_city?: string;
  beneficiary_state?: string;
  beneficiary_postal_code?: string;
}

export interface CashfreeTransferBeneficiaryDetails {
  beneficiary_id?: string;
  beneficiary_name?: string;
  beneficiary_instrument_details?: CashfreeBeneficiaryInstrumentDetails;
  beneficiary_contact_details?: CashfreeBeneficiaryContactDetails;
}

// Create Transfer Request interface
export interface CashfreeCreateTransferRequest {
  transfer_id: string; // Max 40 chars, alphanumeric and underscore
  transfer_amount: number; // Min 1.00
  transfer_currency?: string; // Default: "INR"
  transfer_mode?: CashfreeTransferMode; // Default: "banktransfer"
  beneficiary_details: CashfreeTransferBeneficiaryDetails;
  transfer_remarks?: string; // Max 70 chars
  fundsource_id?: string;
}

// Transfer Response interface
export interface CashfreeTransferResponse {
  transfer_id: string;
  cf_transfer_id: string;
  status: CashfreeTransferStatus;
  status_code?: string;
  status_description?: string;
  beneficiary_details: {
    beneficiary_id?: string;
    beneficiary_instrument_details?: {
      bank_account_number?: string;
      ifsc?: string;
      vpa?: string;
    };
  };
  transfer_amount: number;
  transfer_service_charge?: number;
  transfer_service_tax?: number;
  transfer_mode: string;
  transfer_utr?: string;
  fundsource_id?: string;
  added_on: string;
  updated_on: string;
}

// Get Transfer Status Request interface
export interface CashfreeGetTransferStatusRequest {
  transfer_id?: string;
  cf_transfer_id?: string;
}

// Legacy transfer interfaces - keeping for backward compatibility
export interface CashfreeLegacyCreateTransferRequest {
  bene_id: string;
  amount: number;
  transfer_id: string;
  transfer_mode?:
    | "banktransfer"
    | "imps"
    | "neft"
    | "rtgs"
    | "upi"
    | "paytm"
    | "amazonpay"
    | "card"
    | "cardupi";
  remarks?: string;
  fundsource_id: string;
}

export interface CashfreeLegacyTransferResponse {
  cf_transfer_id: string;
  transfer_id: string;
  beneficiary_details: { beneficiary_id: string };
  transfer_amount: number;
  transfer_service_charge: number;
  transfer_service_tax: number;
  status: string;
  transfer_mode: string;
  fundsource_id: string;
  transfer_utr: string;
  added_on: string;
  updated_on: string;
}

// Settlement interfaces
export interface CashfreeSettlementResponse {
  cf_settlement_id: string;
  settlement_id: string;
  settlement_currency: string;
  settlement_amount: number;
  settlement_status: string;
  settlement_charges: number;
  settlement_tax: number;
  settlement_utr: string;
  settlement_time: string;
  service_charge: number;
  service_tax: number;
  settlement_type: string;
  created_at: string;
}

// Webhook interfaces
export interface CashfreeWebhookData {
  type: string;
  order_id: string;
  cf_order_id: string;
  payment_details?: CashfreePaymentDetails;
  settlement_details?: CashfreeSettlementResponse;
  transfer_details?: CashfreeLegacyTransferResponse;
  signature: string;
  timestamp: string;
}

// Error interfaces
export interface CashfreeError {
  message: string;
  code: string;
  type: string;
}

export interface CashfreeErrorResponse {
  status: string;
  message: string;
  code: string;
  type: string;
}
