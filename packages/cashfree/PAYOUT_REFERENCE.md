# Cashfree Payout API Reference

This document provides a comprehensive reference for implementing Cashfree payout features using the `@acme/cashfree-sdk` package.

## Overview

The Cashfree Payout API allows you to:

- Create and manage beneficiaries (recipients of payouts)
- Initiate transfers/payouts to beneficiaries
- Track payout status and manage beneficiary lifecycle

## API Endpoints

All payout APIs use the base URL:

- **Sandbox**: `https://sandbox.cashfree.com/payout`
- **Production**: `https://api.cashfree.com/payout`

## Authentication

All requests require the following headers:

- `x-client-id`: Your Cashfree App ID
- `x-client-secret`: Your Cashfree Secret Key
- `x-api-version`: API version (default: `2024-01-01`)

## Beneficiary Management

### 1. Create Beneficiary

Creates a new beneficiary in your Cashfree account.

**Endpoint**: `POST /beneficiary`

**Request Body**:

```typescript
interface CashfreeCreateBeneficiaryRequest {
  beneficiary_id: string; // Unique ID (max 50 chars, alphanumeric + _ | .)
  beneficiary_name: string; // Name (max 100 chars, alphabets + spaces)
  beneficiary_instrument_details?: {
    bank_account_number?: string; // 4-25 chars, alphanumeric
    bank_ifsc?: string; // 11 chars IFSC format
    vpa?: string; // Valid UPI VPA
  };
  beneficiary_contact_details?: {
    beneficiary_email?: string; // Max 200 chars
    beneficiary_phone?: string; // 8-12 digits (without +91)
    beneficiary_country_code?: string; // Default: "+91"
    beneficiary_address?: string;
    beneficiary_city?: string;
    beneficiary_state?: string;
    beneficiary_postal_code?: string; // 6 digits
  };
}
```

**Response**:

```typescript
interface CashfreeBeneficiaryResponse {
  beneficiary_id: string;
  beneficiary_name: string;
  beneficiary_instrument_details?: {
    bank_account_number?: string;
    bank_ifsc?: string;
    vpa?: string;
  };
  beneficiary_contact_details?: {
    beneficiary_email?: string;
    beneficiary_phone?: string;
    beneficiary_country_code?: string;
    beneficiary_address?: string;
    beneficiary_city?: string;
    beneficiary_state?: string;
    beneficiary_postal_code?: string;
  };
  beneficiary_status:
    | "VERIFIED"
    | "INVALID"
    | "INITIATED"
    | "CANCELLED"
    | "FAILED"
    | "DELETED";
  added_on: string; // ISO 8601 timestamp
}
```

**Usage Example**:

```typescript
const cashfree = new Cashfree({
  appId: "your_app_id",
  secretKey: "your_secret_key",
  environment: "sandbox",
});

const beneficiaryData = {
  beneficiary_id: "SELLER_123",
  beneficiary_name: "John Doe",
  beneficiary_instrument_details: {
    bank_account_number: "**********",
    bank_ifsc: "HDFC0000001",
  },
  beneficiary_contact_details: {
    beneficiary_email: "<EMAIL>",
    beneficiary_phone: "**********",
    beneficiary_country_code: "+91",
  },
};

const { data, error } = await cashfree.createBeneficiary(beneficiaryData);
```

### 2. Get Beneficiary

Retrieves details of an existing beneficiary.

**Endpoint**: `GET /beneficiary`

**Query Parameters**:

- `beneficiary_id`: Unique beneficiary ID
- OR
- `bank_account_number` + `bank_ifsc`: Bank account details

**Usage Example**:

```typescript
// Get by beneficiary ID
const { data, error } = await cashfree.getBeneficiary({
  beneficiary_id: "SELLER_123",
});

// Get by bank account details
const { data, error } = await cashfree.getBeneficiary({
  bank_account_number: "**********",
  bank_ifsc: "HDFC0000001",
});
```

### 3. Remove Beneficiary

Removes a beneficiary from your account.

**Endpoint**: `DELETE /beneficiary`

**Query Parameters**:

- `beneficiary_id`: Unique beneficiary ID to remove

**Usage Example**:

```typescript
const { data, error } = await cashfree.removeBeneficiary("SELLER_123");
```

## Beneficiary Status Values

- **VERIFIED**: Beneficiary is verified and available for payouts
- **INVALID**: Beneficiary details are invalid
- **INITIATED**: Beneficiary verification in progress
- **CANCELLED**: Beneficiary verification was cancelled
- **FAILED**: Beneficiary verification failed
- **DELETED**: Beneficiary has been removed

## Error Handling

All API methods return a consistent response format:

```typescript
interface CashfreeResponse<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
}
```

Common error scenarios:

- **400 Bad Request**: Invalid parameters or missing required fields
- **403 Forbidden**: API not enabled or insufficient permissions
- **404 Not Found**: Beneficiary not found
- **409 Conflict**: Duplicate beneficiary ID or bank account
- **422 Unprocessable**: Business rule violations (e.g., same as source account)

## Best Practices

1. **Unique Beneficiary IDs**: Use meaningful, unique identifiers (e.g., `seller_${userId}`)
2. **Validation**: Always validate bank account and IFSC before creating beneficiaries
3. **Error Handling**: Implement proper error handling for all API calls
4. **Status Checking**: Verify beneficiary status before initiating transfers
5. **Idempotency**: Handle duplicate creation attempts gracefully

## Integration Examples

### Creating a Seller Beneficiary

```typescript
async function createSellerBeneficiary(sellerId: string, sellerData: any) {
  const beneficiaryRequest = {
    beneficiary_id: `seller_${sellerId}`,
    beneficiary_name: sellerData.fullName,
    beneficiary_instrument_details: {
      bank_account_number: sellerData.bankAccount,
      bank_ifsc: sellerData.ifsc,
    },
    beneficiary_contact_details: {
      beneficiary_email: sellerData.email,
      beneficiary_phone: sellerData.phone,
      beneficiary_country_code: "+91",
    },
  };

  const { data, error } = await cashfree.createBeneficiary(beneficiaryRequest);

  if (error) {
    console.error("Failed to create beneficiary:", error);
    throw error;
  }

  return data;
}
```

### Checking Beneficiary Status

```typescript
async function isBeneficiaryReady(beneficiaryId: string): Promise<boolean> {
  const { data, error } = await cashfree.getBeneficiary({
    beneficiary_id: beneficiaryId,
  });

  if (error || !data) {
    return false;
  }

  return data.beneficiary_status === "VERIFIED";
}
```

## Transfer Management (Phase 2)

### 1. Create Transfer

Initiates a money transfer to a beneficiary.

**Endpoint**: `POST /transfers`

**Request Body**:

```typescript
interface CashfreeCreateTransferRequest {
  transfer_id: string; // Max 40 chars, alphanumeric + underscore
  transfer_amount: number; // Min 1.00
  transfer_currency?: string; // Default: "INR"
  transfer_mode?: CashfreeTransferMode; // Default: "banktransfer"
  beneficiary_details: CashfreeTransferBeneficiaryDetails;
  transfer_remarks?: string; // Max 70 chars
  fundsource_id?: string;
}

type CashfreeTransferMode =
  | "banktransfer"
  | "imps"
  | "neft"
  | "rtgs"
  | "upi"
  | "paytm"
  | "amazonpay"
  | "card"
  | "cardupi";
```

**Response**:

```typescript
interface CashfreeTransferResponse {
  transfer_id: string;
  cf_transfer_id: string;
  status: CashfreeTransferStatus;
  status_code?: string;
  status_description?: string;
  beneficiary_details: {
    beneficiary_id?: string;
    beneficiary_instrument_details?: {
      bank_account_number?: string;
      ifsc?: string;
      vpa?: string;
    };
  };
  transfer_amount: number;
  transfer_service_charge?: number;
  transfer_service_tax?: number;
  transfer_mode: string;
  transfer_utr?: string;
  fundsource_id?: string;
  added_on: string;
  updated_on: string;
}

type CashfreeTransferStatus =
  | "RECEIVED"
  | "APPROVAL_PENDING"
  | "PENDING"
  | "SUCCESS"
  | "FAILED"
  | "REJECTED"
  | "REVERSED";
```

**Usage Examples**:

```typescript
// Transfer to existing beneficiary
const transferRequest = {
  transfer_id: "TXN_123456",
  transfer_amount: 1000.0,
  transfer_mode: "imps",
  beneficiary_details: {
    beneficiary_id: "seller_123",
  },
  transfer_remarks: "Payment for order #12345",
};

const { data, error } = await cashfree.createTransfer(transferRequest);

// Transfer with inline beneficiary details
const inlineTransferRequest = {
  transfer_id: "TXN_789012",
  transfer_amount: 500.0,
  transfer_mode: "upi",
  beneficiary_details: {
    beneficiary_name: "John Doe",
    beneficiary_instrument_details: {
      vpa: "john@paytm",
    },
  },
};

const { data, error } = await cashfree.createTransfer(inlineTransferRequest);
```

### 2. Get Transfer Status

Retrieves the status of a transfer.

**Endpoint**: `GET /transfers`

**Query Parameters**:

- `transfer_id`: Your unique transfer ID
- OR
- `cf_transfer_id`: Cashfree's transfer ID

**Usage Examples**:

```typescript
// Get status by transfer ID
const { data, error } = await cashfree.getTransferStatus({
  transfer_id: "TXN_123456",
});

// Get status by Cashfree transfer ID
const { data, error } = await cashfree.getTransferStatus({
  cf_transfer_id: "CF_123456789",
});

// Backward compatibility - string parameter
const { data, error } = await cashfree.getTransferStatus("TXN_123456");
```

## Transfer Status Values

- **RECEIVED**: Transfer received by Cashfree for processing
- **APPROVAL_PENDING**: Transfer requires manual approval
- **PENDING**: Transfer initiated and pending at bank
- **SUCCESS**: Transfer completed successfully
- **FAILED**: Transfer failed (amount will be refunded)
- **REJECTED**: Transfer rejected by Cashfree
- **REVERSED**: Transfer reversed by bank

## Transfer Modes

- **banktransfer**: Standard bank transfer (default)
- **imps**: Immediate Payment Service (faster)
- **neft**: National Electronic Funds Transfer
- **rtgs**: Real Time Gross Settlement
- **upi**: Unified Payments Interface
- **paytm**: Paytm wallet transfer
- **amazonpay**: Amazon Pay wallet transfer
- **card**: Card-based transfer
- **cardupi**: Card UPI transfer

## Helper Functions

The SDK provides utility functions for easier transfer management:

```typescript
import {
  createSimpleTransferRequest,
  createTransferId,
  validateTransferRequest,
} from "@acme/cashfree-sdk";

// Create a simple transfer to existing beneficiary
const transferRequest = createSimpleTransferRequest({
  transferId: "TXN_123456",
  amount: 1000.0,
  beneficiaryId: "seller_123",
  transferMode: "imps",
  remarks: "Payment for services",
});

// Validate transfer request
const errors = validateTransferRequest(transferRequest);
if (errors.length > 0) {
  console.error("Validation errors:", errors);
}

// Generate unique transfer ID
const transferId = createTransferId("payout", Date.now().toString());
```

## Next Steps

Phase 3 will include:

- Bulk transfers
- Transfer scheduling
- Webhook handling for real-time updates
- Advanced reporting and analytics
